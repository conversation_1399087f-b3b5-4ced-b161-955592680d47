<!-- markdownlint-disable -->
<p align="center">
  <!-- github-banner-start -->
  <img src="https://raw.githubusercontent.com/litestar-org/branding/main/assets/Branding%20-%20SVG%20-%20Transparent/Logo%20-%20Banner%20-%20Inline%20-%20Light.svg#gh-light-mode-only" alt="Litestar Logo - Light" width="100%" height="auto" />
  <img src="https://raw.githubusercontent.com/litestar-org/branding/main/assets/Branding%20-%20SVG%20-%20Transparent/Logo%20-%20Banner%20-%20Inline%20-%20Dark.svg#gh-dark-mode-only" alt="Litestar Logo - Dark" width="100%" height="auto" />
  <!-- github-banner-end -->
</p>
<!-- markdownlint-restore -->

<div align="center">

<!-- prettier-ignore-start -->

| Project   |     | Status                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|-----------|:----|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| CI/CD     |     | [![Latest Release](https://github.com/litestar-org/litestar/actions/workflows/publish.yml/badge.svg)](https://github.com/litestar-org/litestar/actions/workflows/publish.yml) [![ci](https://github.com/litestar-org/litestar/actions/workflows/ci.yml/badge.svg)](https://github.com/litestar-org/litestar/actions/workflows/ci.yml) [![Documentation Building](https://github.com/litestar-org/litestar/actions/workflows/docs.yml/badge.svg?branch=main)](https://github.com/litestar-org/litestar/actions/workflows/docs.yml)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| Quality   |     | [![Coverage](https://codecov.io/github/litestar-org/litestar/graph/badge.svg?token=vKez4Pycrc)](https://codecov.io/github/litestar-org/litestar)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| Package   |     | [![PyPI - Version](https://img.shields.io/pypi/v/litestar?labelColor=202235&color=edb641&logo=python&logoColor=edb641)](https://badge.fury.io/py/litestar) ![PyPI - Support Python Versions](https://img.shields.io/pypi/pyversions/litestar?labelColor=202235&color=edb641&logo=python&logoColor=edb641) ![Starlite PyPI - Downloads](https://img.shields.io/pypi/dm/starlite?logo=python&label=starlite%20downloads&labelColor=202235&color=edb641&logoColor=edb641) ![Litestar PyPI - Downloads](https://img.shields.io/pypi/dm/litestar?logo=python&label=litestar%20downloads&labelColor=202235&color=edb641&logoColor=edb641)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| Community |     | [![Reddit](https://img.shields.io/reddit/subreddit-subscribers/litestarapi?label=r%2FLitestar&logo=reddit&labelColor=202235&color=edb641&logoColor=edb641)](https://reddit.com/r/litestarapi) [![Discord](https://img.shields.io/discord/919193495116337154?labelColor=202235&color=edb641&label=chat%20on%20discord&logo=discord&logoColor=edb641)](https://discord.gg/litestar) [![Matrix](https://img.shields.io/badge/chat%20on%20Matrix-bridged-202235?labelColor=202235&color=edb641&logo=matrix&logoColor=edb641)](https://matrix.to/#/#litestar:matrix.org) [![Medium](https://img.shields.io/badge/Medium-202235?labelColor=202235&color=edb641&logo=medium&logoColor=edb641)](https://blog.litestar.dev) [![Twitter](https://img.shields.io/twitter/follow/LitestarAPI?labelColor=202235&color=edb641&logo=twitter&logoColor=edb641&style=flat)](https://twitter.com/LitestarAPI) [![Blog](https://img.shields.io/badge/Blog-litestar.dev-202235?logo=blogger&labelColor=202235&color=edb641&logoColor=edb641)](https://blog.litestar.dev)                                                                                                                                                                                                                          |
| Meta      |     | [![Litestar Project](https://img.shields.io/badge/Litestar%20Org-%E2%AD%90%20Litestar-202235.svg?logo=python&labelColor=202235&color=edb641&logoColor=edb641)](https://github.com/litestar-org/litestar) [![types - Mypy](https://img.shields.io/badge/types-Mypy-202235.svg?logo=python&labelColor=202235&color=edb641&logoColor=edb641)](https://github.com/python/mypy) [![License - MIT](https://img.shields.io/badge/license-MIT-202235.svg?logo=python&labelColor=202235&color=edb641&logoColor=edb641)](https://spdx.org/licenses/) [![Litestar Sponsors](https://img.shields.io/badge/Sponsor-%E2%9D%A4-%23edb641.svg?&logo=github&logoColor=edb641&labelColor=202235)](https://github.com/sponsors/litestar-org) [![linting - Ruff](https://img.shields.io/endpoint?url=https://raw.githubusercontent.com/charliermarsh/ruff/main/assets/badge/v2.json&labelColor=202235)](https://github.com/astral-sh/ruff) [![code style - Ruff](https://img.shields.io/endpoint?url=https://raw.githubusercontent.com/astral-sh/ruff/main/assets/badge/format.json&labelColor=202235)](https://github.com/psf/black) [![All Contributors](https://img.shields.io/github/all-contributors/litestar-org/litestar?labelColor=202235&color=edb641&logoColor=edb641)](#contributors-) |

<!-- prettier-ignore-end -->
</div>

<hr>

Litestar is a powerful, flexible yet opinionated ASGI framework, focused on
building APIs. It offers high-performance data validation, dependency injection,
first-class ORM integration, authorization primitives, a rich plugin API, middleware,
and much more that's needed to get applications up and running.

Check out the [documentation 📚](https://docs.litestar.dev/) for a detailed overview of
its features!

Additionally, the [Litestar fullstack repository](https://github.com/litestar-org/litestar-fullstack)
can give you a good impression how a fully fledged Litestar application may look.

<details>
<summary>Table of Contents</summary>

- [Installation](#installation)
  - [Quick Start](#quick-start)
- [Core Features](#core-features)
  - [Example Applications](#example-applications)
- [Features](#features)
  - [Class-based Controllers](#class-based-controllers)
  - [Data Parsing, Type Hints, and Msgspec](#data-parsing-type-hints-and-msgspec)
  - [Plugin System, ORM support, and DTOs](#plugin-system-orm-support-and-dtos)
  - [OpenAPI](#openapi)
  - [Dependency Injection](#dependency-injection)
  - [Middleware](#middleware)
  - [Route Guards](#route-guards)
  - [Request Life Cycle Hooks](#request-life-cycle-hooks)
- [Performance](#performance)
- [Contributing](#contributing)

</details>

## Installation

```shell
pip install litestar
```
or to include the CLI and a server (uvicorn) for running your application:
```shell
pip install 'litestar[standard]'
```

## Quick Start

```python title="app.py"
from litestar import Litestar, get

@get("/")
async def hello_world() -> dict[str, str]:
    """Keeping the tradition alive with hello world."""
    return {"hello": "world"}

app = Litestar(route_handlers=[hello_world])
```

And run it with

```bash
litestar run
```


## Core Features

- [Class based controllers](#class-based-controllers)
- [Dependency Injection](#dependency-injection)
- [Layered Middleware](#middleware)
- [Plugin System](#plugin-system-orm-support-and-dtos)
- [OpenAPI 3.1 schema generation](#openapi)
- [Life Cycle Hooks](#request-life-cycle-hooks)
- [Route Guards based Authorization](#route-guards)
- Support for `dataclasses`, `TypedDict`, [`msgspec`](https://jcristharif.com/msgspec/), [pydantic version 1 and version 2 (even within the same application)](https://docs.pydantic.dev/latest/) and [(c)attrs](https://catt.rs/en/stable/)
  [msgspec](https://github.com/jcrist/msgspec) and [attrs](https://www.attrs.org/en/stable/)
- Layered parameter declaration
- Support for [RFC 9457](https://datatracker.ietf.org/doc/html/rfc9457) standardized "Problem Detail" error responses
- [Automatic API documentation with](#redoc-swagger-ui-and-stoplight-elements-api-documentation):
  - [Scalar](https://github.com/scalar/scalar/)
  - [RapiDoc](https://github.com/rapi-doc/RapiDoc)
  - [Redoc](https://github.com/Redocly/redoc)
  - [Stoplight Elements](https://github.com/stoplightio/elements)
  - [Swagger-UI](https://swagger.io/tools/swagger-ui/)
- [Trio](https://trio.readthedocs.io/en/stable/) support (built-in, via [AnyIO](https://anyio.readthedocs.io/))
- Ultra-fast validation, serialization and deserialization using [msgspec](https://github.com/jcrist/msgspec)
- [SQLAlchemy integration](https://docs.advanced-alchemy.litestar.dev/latest/)

## Example Applications

<details>
<summary>Pre-built Example Apps</summary>

- [litestar-hello-world](https://github.com/litestar-org/litestar-hello-world): A bare-minimum application setup. Great
for testing and POC work.
- [litestar-fullstack](https://github.com/litestar-org/litestar-fullstack): A reference application that contains most of the boilerplate required for a web application.
  It features a Litestar app configured with best practices, SQLAlchemy 2.0 and SAQ, a frontend integrated with Vitejs and Jinja2 templates, Docker, and more. Like all
  Litestar projects, this application is open to contributions, big and small.
</details>

## Sponsors

Litestar is an open-source project, and we enjoy the support of our sponsors to help fund the exciting
work we do.

A **huge** thanks to our sponsors:

[//]: # "Note to maintainers: Highest sponsors first; no more than 3 per row - create new div if needed"

<a href="https://github.com/scalar/scalar/?utm_source=litestar&utm_medium=website&utm_campaign=main-badge" target="_blank" title="Scalar.com - Document, Discover and Test APIs with Scalar."><img src="https://raw.githubusercontent.com/litestar-org/branding/main/assets/sponsors/scalar.svg" width="180" alt="Scalar.com"></a>
<a href="https://telemetrysports.com/" title="Telemetry Sports - Changing the way data influences the sports experience"><img src="https://raw.githubusercontent.com/litestar-org/branding/main/assets/sponsors/telemetry-sports/unofficial-telemetry-whitebg.svg" width="150" alt="Telemetry Sports"></a>

<a href="https://docs.litestar.dev/dev/#sponsors" class="external-link" target="_blank">Check out our sponsors in the docs</a>

If you would like to support the work that we do please consider [becoming a sponsor][sponsor-polar]
via [Polar.sh][sponsor-polar] (preferred), [GitHub][sponsor-github] or [Open Collective][sponsor-oc].

Also, exclusively with [Polar][sponsor-polar], you can engage in pledge-based sponsorships.

[sponsor-github]: https://github.com/sponsors/litestar-org
[sponsor-oc]: https://opencollective.com/litestar
[sponsor-polar]: https://polar.sh/litestar-org

## Features

### Class-based Controllers

While supporting function-based route handlers, Litestar also supports and promotes python OOP using class based
controllers:

<details>
<summary>Example for class-based controllers</summary>

```python title="my_app/controllers/user.py"
from typing import List, Optional
from datetime import datetime

from litestar import Controller, get, post, put, patch, delete
from litestar.dto import DTOData
from pydantic import UUID4

from my_app.models import User, PartialUserDTO


class UserController(Controller):
    path = "/users"

    @post()
    async def create_user(self, data: User) -> User: ...

    @get()
    async def list_users(self) -> List[User]: ...

    @get(path="/{date:int}")
    async def list_new_users(self, date: datetime) -> List[User]: ...

    @patch(path="/{user_id:uuid}", dto=PartialUserDTO)
    async def partial_update_user(
        self, user_id: UUID4, data: DTOData[PartialUserDTO]
    ) -> User: ...

    @put(path="/{user_id:uuid}")
    async def update_user(self, user_id: UUID4, data: User) -> User: ...

    @get(path="/{user_name:str}")
    async def get_user_by_name(self, user_name: str) -> Optional[User]: ...

    @get(path="/{user_id:uuid}")
    async def get_user(self, user_id: UUID4) -> User: ...

    @delete(path="/{user_id:uuid}")
    async def delete_user(self, user_id: UUID4) -> None: ...
```

</details>

### Data Parsing, Type Hints, and Msgspec

Litestar is rigorously typed, and it enforces typing. For example, if you forget to type a return value for a route
handler, an exception will be raised. The reason for this is that Litestar uses typing data to generate OpenAPI specs,
as well as to validate and parse data. Thus, typing is essential to the framework.

Furthermore, Litestar allows extending its support using plugins.

### Plugin System, ORM support, and DTOs

Litestar has a plugin system that allows the user to extend serialization/deserialization, OpenAPI generation, and other
features.

It ships with a builtin plugin for SQL Alchemy, which allows the user to use SQLAlchemy declarative classes "natively"
i.e., as type parameters that will be serialized/deserialized and to return them as values from route
handlers.

Litestar also supports the programmatic creation of DTOs with a `DTOFactory` class, which also supports the use of
plugins.

### OpenAPI

Litestar has custom logic to generate OpenAPI 3.1.0 schema, include optional generation of examples using the
[`polyfactory`](https://pypi.org/project/polyfactory/) library.

#### ReDoc, Swagger-UI and Stoplight Elements API Documentation

Litestar serves the documentation from the generated OpenAPI schema with:

- [ReDoc](https://redoc.ly/)
- [Swagger-UI](https://swagger.io/tools/swagger-ui/)
- [Stoplight Elements](https://github.com/stoplightio/elements)
- [RapiDoc](https://rapidocweb.com/)

All these are available and enabled by default.

### Dependency Injection

Litestar has a simple but powerful DI system inspired by pytest. You can define named dependencies - sync or async - at
different levels of the application, and then selective use or overwrite them.

<details>
<summary>Example for DI</summary>

```python
from litestar import Litestar, get
from litestar.di import Provide


async def my_dependency() -> str: ...


@get("/")
async def index(injected: str) -> str:
    return injected


app = Litestar([index], dependencies={"injected": Provide(my_dependency)})
```

</details>

### Middleware

Litestar supports typical ASGI middleware and ships with middlewares to handle things such as

- CORS
- CSRF
- Rate limiting
- GZip and Brotli compression
- Client- and server-side sessions

### Route Guards

Litestar has an authorization mechanism called `guards`, which allows the user to define guard functions at different
level of the application (app, router, controller etc.) and validate the request before hitting the route handler
function.

<details>
<summary>Example for route guards</summary>

```python
from litestar import Litestar, get

from litestar.connection import ASGIConnection
from litestar.handlers.base import BaseRouteHandler
from litestar.exceptions import NotAuthorizedException


async def is_authorized(connection: ASGIConnection, handler: BaseRouteHandler) -> None:
    # validate authorization
    # if not authorized, raise NotAuthorizedException
    raise NotAuthorizedException()


@get("/", guards=[is_authorized])
async def index() -> None: ...


app = Litestar([index])
```

</details>

### Request Life Cycle Hooks

Litestar supports request life cycle hooks, similarly to Flask - i.e. `before_request` and `after_request`

## Performance

Litestar is fast. It is on par with, or significantly faster than comparable ASGI frameworks.

You can see and run the benchmarks [here](https://github.com/litestar-org/api-performance-tests),
or read more about it [here](https://docs.litestar.dev/latest/benchmarks) in our documentation.

## Contributing

Litestar is open to contributions big and small. You can always [join our discord](https://discord.gg/litestar) server
or [join our Matrix](https://matrix.to/#/#litestar:matrix.org) space
to discuss contributions and project maintenance. For guidelines on how to contribute, please
see [the contribution guide](CONTRIBUTING.rst).

<!-- contributors-start -->

## Contributors ✨

<details>

<summary>Thanks goes to these wonderful people:</summary>
<a href="https://allcontributors.org/docs/en/emoji-key">Emoji Key </a>

<!-- ALL-CONTRIBUTORS-LIST:START - Do not remove or modify this section -->
<!-- prettier-ignore-start -->
<!-- markdownlint-disable -->
<table>
  <tbody>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://www.linkedin.com/in/nhirschfeld/"><img src="https://avatars.githubusercontent.com/u/30733348?v=4?s=100" width="100px;" alt="Na'aman Hirschfeld"/><br /><sub><b>Na'aman Hirschfeld</b></sub></a><br /><a href="#maintenance-Goldziher" title="Maintenance">🚧</a> <a href="https://github.com/litestar-org/litestar/commits?author=Goldziher" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=Goldziher" title="Documentation">📖</a> <a href="https://github.com/litestar-org/litestar/commits?author=Goldziher" title="Tests">⚠️</a> <a href="#ideas-Goldziher" title="Ideas, Planning, & Feedback">🤔</a> <a href="#example-Goldziher" title="Examples">💡</a> <a href="https://github.com/litestar-org/litestar/issues?q=author%3AGoldziher" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/peterschutt"><img src="https://avatars.githubusercontent.com/u/20659309?v=4?s=100" width="100px;" alt="Peter Schutt"/><br /><sub><b>Peter Schutt</b></sub></a><br /><a href="#maintenance-peterschutt" title="Maintenance">🚧</a> <a href="https://github.com/litestar-org/litestar/commits?author=peterschutt" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=peterschutt" title="Documentation">📖</a> <a href="https://github.com/litestar-org/litestar/commits?author=peterschutt" title="Tests">⚠️</a> <a href="#ideas-peterschutt" title="Ideas, Planning, & Feedback">🤔</a> <a href="#example-peterschutt" title="Examples">💡</a> <a href="https://github.com/litestar-org/litestar/issues?q=author%3Apeterschutt" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://ashwinvin.github.io"><img src="https://avatars.githubusercontent.com/u/38067089?v=4?s=100" width="100px;" alt="Ashwin Vinod"/><br /><sub><b>Ashwin Vinod</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=ashwinvin" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=ashwinvin" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="http://www.damiankress.de"><img src="https://avatars.githubusercontent.com/u/28515387?v=4?s=100" width="100px;" alt="Damian"/><br /><sub><b>Damian</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=dkress59" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://remotepixel.ca"><img src="https://avatars.githubusercontent.com/u/10407788?v=4?s=100" width="100px;" alt="Vincent Sarago"/><br /><sub><b>Vincent Sarago</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=vincentsarago" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://hotfix.guru"><img src="https://avatars.githubusercontent.com/u/5310116?v=4?s=100" width="100px;" alt="Jonas Krüger Svensson"/><br /><sub><b>Jonas Krüger Svensson</b></sub></a><br /><a href="#platform-JonasKs" title="Packaging/porting to new platform">📦</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/sondrelg"><img src="https://avatars.githubusercontent.com/u/25310870?v=4?s=100" width="100px;" alt="Sondre Lillebø Gundersen"/><br /><sub><b>Sondre Lillebø Gundersen</b></sub></a><br /><a href="#platform-sondrelg" title="Packaging/porting to new platform">📦</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/vrslev"><img src="https://avatars.githubusercontent.com/u/75225148?v=4?s=100" width="100px;" alt="Lev"/><br /><sub><b>Lev</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=vrslev" title="Code">💻</a> <a href="#ideas-vrslev" title="Ideas, Planning, & Feedback">🤔</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/timwedde"><img src="https://avatars.githubusercontent.com/u/20231751?v=4?s=100" width="100px;" alt="Tim Wedde"/><br /><sub><b>Tim Wedde</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=timwedde" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/tclasen"><img src="https://avatars.githubusercontent.com/u/11999013?v=4?s=100" width="100px;" alt="Tory Clasen"/><br /><sub><b>Tory Clasen</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=tclasen" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="http://t.me/Bobronium"><img src="https://avatars.githubusercontent.com/u/36469655?v=4?s=100" width="100px;" alt="Arseny Boykov"/><br /><sub><b>Arseny Boykov</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=Bobronium" title="Code">💻</a> <a href="#ideas-Bobronium" title="Ideas, Planning, & Feedback">🤔</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/yudjinn"><img src="https://avatars.githubusercontent.com/u/7493084?v=4?s=100" width="100px;" alt="Jacob Rodgers"/><br /><sub><b>Jacob Rodgers</b></sub></a><br /><a href="#example-yudjinn" title="Examples">💡</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/danesolberg"><img src="https://avatars.githubusercontent.com/u/25882507?v=4?s=100" width="100px;" alt="Dane Solberg"/><br /><sub><b>Dane Solberg</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=danesolberg" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/madlad33"><img src="https://avatars.githubusercontent.com/u/54079440?v=4?s=100" width="100px;" alt="madlad33"/><br /><sub><b>madlad33</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=madlad33" title="Code">💻</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="http://matthewtyleraylward.com"><img src="https://avatars.githubusercontent.com/u/19205392?v=4?s=100" width="100px;" alt="Matthew Aylward "/><br /><sub><b>Matthew Aylward </b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=Butch78" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/Joko013"><img src="https://avatars.githubusercontent.com/u/30841710?v=4?s=100" width="100px;" alt="Jan Klima"/><br /><sub><b>Jan Klima</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=Joko013" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/i404788"><img src="https://avatars.githubusercontent.com/u/50617709?v=4?s=100" width="100px;" alt="C2D"/><br /><sub><b>C2D</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=i404788" title="Tests">⚠️</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/to-ph"><img src="https://avatars.githubusercontent.com/u/84818322?v=4?s=100" width="100px;" alt="to-ph"/><br /><sub><b>to-ph</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=to-ph" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://imbev.gitlab.io/site"><img src="https://avatars.githubusercontent.com/u/105524473?v=4?s=100" width="100px;" alt="imbev"/><br /><sub><b>imbev</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=imbev" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://git.roboces.dev/catalin"><img src="https://avatars.githubusercontent.com/u/45485069?v=4?s=100" width="100px;" alt="cătălin"/><br /><sub><b>cătălin</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=185504a9" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/Seon82"><img src="https://avatars.githubusercontent.com/u/46298009?v=4?s=100" width="100px;" alt="Seon82"/><br /><sub><b>Seon82</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=Seon82" title="Documentation">📖</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/slavugan"><img src="https://avatars.githubusercontent.com/u/8457612?v=4?s=100" width="100px;" alt="Slava"/><br /><sub><b>Slava</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=slavugan" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/Harry-Lees"><img src="https://avatars.githubusercontent.com/u/52263746?v=4?s=100" width="100px;" alt="Harry"/><br /><sub><b>Harry</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=Harry-Lees" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=Harry-Lees" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/cofin"><img src="https://avatars.githubusercontent.com/u/204685?v=4?s=100" width="100px;" alt="Cody Fincher"/><br /><sub><b>Cody Fincher</b></sub></a><br /><a href="#maintenance-cofin" title="Maintenance">🚧</a> <a href="https://github.com/litestar-org/litestar/commits?author=cofin" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=cofin" title="Documentation">📖</a> <a href="https://github.com/litestar-org/litestar/commits?author=cofin" title="Tests">⚠️</a> <a href="#ideas-cofin" title="Ideas, Planning, & Feedback">🤔</a> <a href="#example-cofin" title="Examples">💡</a> <a href="https://github.com/litestar-org/litestar/issues?q=author%3Acofin" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://www.patreon.com/cclauss"><img src="https://avatars.githubusercontent.com/u/3709715?v=4?s=100" width="100px;" alt="Christian Clauss"/><br /><sub><b>Christian Clauss</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=cclauss" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/josepdaniel"><img src="https://avatars.githubusercontent.com/u/36941460?v=4?s=100" width="100px;" alt="josepdaniel"/><br /><sub><b>josepdaniel</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=josepdaniel" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/devtud"><img src="https://avatars.githubusercontent.com/u/6808024?v=4?s=100" width="100px;" alt="devtud"/><br /><sub><b>devtud</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/issues?q=author%3Adevtud" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/nramos0"><img src="https://avatars.githubusercontent.com/u/35410160?v=4?s=100" width="100px;" alt="Nicholas Ramos"/><br /><sub><b>Nicholas Ramos</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=nramos0" title="Code">💻</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://twitter.com/seladb"><img src="https://avatars.githubusercontent.com/u/9059541?v=4?s=100" width="100px;" alt="seladb"/><br /><sub><b>seladb</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=seladb" title="Documentation">📖</a> <a href="https://github.com/litestar-org/litestar/commits?author=seladb" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/aedify-swi"><img src="https://avatars.githubusercontent.com/u/66629131?v=4?s=100" width="100px;" alt="Simon Wienhöfer"/><br /><sub><b>Simon Wienhöfer</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=aedify-swi" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/mobiusxs"><img src="https://avatars.githubusercontent.com/u/57055149?v=4?s=100" width="100px;" alt="MobiusXS"/><br /><sub><b>MobiusXS</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=mobiusxs" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="http://aidansimard.dev"><img src="https://avatars.githubusercontent.com/u/73361895?v=4?s=100" width="100px;" alt="Aidan Simard"/><br /><sub><b>Aidan Simard</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=Aidan-Simard" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/waweber"><img src="https://avatars.githubusercontent.com/u/714224?v=4?s=100" width="100px;" alt="wweber"/><br /><sub><b>wweber</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=waweber" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="http://scolvin.com"><img src="https://avatars.githubusercontent.com/u/4039449?v=4?s=100" width="100px;" alt="Samuel Colvin"/><br /><sub><b>Samuel Colvin</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=samuelcolvin" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/toudi"><img src="https://avatars.githubusercontent.com/u/81148?v=4?s=100" width="100px;" alt="Mateusz Mikołajczyk"/><br /><sub><b>Mateusz Mikołajczyk</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=toudi" title="Code">💻</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/Alex-CodeLab"><img src="https://avatars.githubusercontent.com/u/1678423?v=4?s=100" width="100px;" alt="Alex "/><br /><sub><b>Alex </b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=Alex-CodeLab" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/odiseo0"><img src="https://avatars.githubusercontent.com/u/87550035?v=4?s=100" width="100px;" alt="Odiseo"/><br /><sub><b>Odiseo</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=odiseo0" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/ingjavierpinilla"><img src="https://avatars.githubusercontent.com/u/36714646?v=4?s=100" width="100px;" alt="Javier  Pinilla"/><br /><sub><b>Javier  Pinilla</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=ingjavierpinilla" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/Chaoyingz"><img src="https://avatars.githubusercontent.com/u/32626585?v=4?s=100" width="100px;" alt="Chaoying"/><br /><sub><b>Chaoying</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=Chaoyingz" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/infohash"><img src="https://avatars.githubusercontent.com/u/46137868?v=4?s=100" width="100px;" alt="infohash"/><br /><sub><b>infohash</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=infohash" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://www.linkedin.com/in/john-ingles/"><img src="https://avatars.githubusercontent.com/u/35442886?v=4?s=100" width="100px;" alt="John Ingles"/><br /><sub><b>John Ingles</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=john-ingles" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/h0rn3t"><img src="https://avatars.githubusercontent.com/u/1213719?v=4?s=100" width="100px;" alt="Eugene"/><br /><sub><b>Eugene</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=h0rn3t" title="Tests">⚠️</a> <a href="https://github.com/litestar-org/litestar/commits?author=h0rn3t" title="Code">💻</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/jonadaly"><img src="https://avatars.githubusercontent.com/u/26462826?v=4?s=100" width="100px;" alt="Jon Daly"/><br /><sub><b>Jon Daly</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=jonadaly" title="Documentation">📖</a> <a href="https://github.com/litestar-org/litestar/commits?author=jonadaly" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://harshallaheri.me/"><img src="https://avatars.githubusercontent.com/u/73422191?v=4?s=100" width="100px;" alt="Harshal Laheri"/><br /><sub><b>Harshal Laheri</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=Harshal6927" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=Harshal6927" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/sorasful"><img src="https://avatars.githubusercontent.com/u/32820423?v=4?s=100" width="100px;" alt="Téva KRIEF"/><br /><sub><b>Téva KRIEF</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=sorasful" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/jtraub"><img src="https://avatars.githubusercontent.com/u/153191?v=4?s=100" width="100px;" alt="Konstantin Mikhailov"/><br /><sub><b>Konstantin Mikhailov</b></sub></a><br /><a href="#maintenance-jtraub" title="Maintenance">🚧</a> <a href="https://github.com/litestar-org/litestar/commits?author=jtraub" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=jtraub" title="Documentation">📖</a> <a href="https://github.com/litestar-org/litestar/commits?author=jtraub" title="Tests">⚠️</a> <a href="#ideas-jtraub" title="Ideas, Planning, & Feedback">🤔</a> <a href="#example-jtraub" title="Examples">💡</a> <a href="https://github.com/litestar-org/litestar/issues?q=author%3Ajtraub" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="14.28%"><a href="http://linkedin.com/in/mitchell-henry334/"><img src="https://avatars.githubusercontent.com/u/17354727?v=4?s=100" width="100px;" alt="Mitchell Henry"/><br /><sub><b>Mitchell Henry</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=devmitch" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/chbndrhnns"><img src="https://avatars.githubusercontent.com/u/7534547?v=4?s=100" width="100px;" alt="chbndrhnns"/><br /><sub><b>chbndrhnns</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=chbndrhnns" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/nielsvanhooy"><img src="https://avatars.githubusercontent.com/u/40770348?v=4?s=100" width="100px;" alt="nielsvanhooy"/><br /><sub><b>nielsvanhooy</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=nielsvanhooy" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/issues?q=author%3Anielsvanhooy" title="Bug reports">🐛</a> <a href="https://github.com/litestar-org/litestar/commits?author=nielsvanhooy" title="Tests">⚠️</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/provinzkraut"><img src="https://avatars.githubusercontent.com/u/25355197?v=4?s=100" width="100px;" alt="provinzkraut"/><br /><sub><b>provinzkraut</b></sub></a><br /><a href="#maintenance-provinzkraut" title="Maintenance">🚧</a> <a href="https://github.com/litestar-org/litestar/commits?author=provinzkraut" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=provinzkraut" title="Documentation">📖</a> <a href="https://github.com/litestar-org/litestar/commits?author=provinzkraut" title="Tests">⚠️</a> <a href="#ideas-provinzkraut" title="Ideas, Planning, & Feedback">🤔</a> <a href="#example-provinzkraut" title="Examples">💡</a> <a href="https://github.com/litestar-org/litestar/issues?q=author%3Aprovinzkraut" title="Bug reports">🐛</a> <a href="#design-provinzkraut" title="Design">🎨</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/jab"><img src="https://avatars.githubusercontent.com/u/64992?v=4?s=100" width="100px;" alt="Joshua Bronson"/><br /><sub><b>Joshua Bronson</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=jab" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="http://linkedin.com/in/roman-reznikov"><img src="https://avatars.githubusercontent.com/u/44291988?v=4?s=100" width="100px;" alt="Roman Reznikov"/><br /><sub><b>Roman Reznikov</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=ReznikovRoman" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="http://mookrs.com"><img src="https://avatars.githubusercontent.com/u/985439?v=4?s=100" width="100px;" alt="mookrs"/><br /><sub><b>mookrs</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=mookrs" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="http://mike.depalatis.net"><img src="https://avatars.githubusercontent.com/u/2805515?v=4?s=100" width="100px;" alt="Mike DePalatis"/><br /><sub><b>Mike DePalatis</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=mivade" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/pemocarlo"><img src="https://avatars.githubusercontent.com/u/7297323?v=4?s=100" width="100px;" alt="Carlos Alberto Pérez-Molano"/><br /><sub><b>Carlos Alberto Pérez-Molano</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=pemocarlo" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://www.bestcryptocodes.com"><img src="https://avatars.githubusercontent.com/u/114229148?v=4?s=100" width="100px;" alt="ThinksFast"/><br /><sub><b>ThinksFast</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=ThinksFast" title="Tests">⚠️</a> <a href="https://github.com/litestar-org/litestar/commits?author=ThinksFast" title="Documentation">📖</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/ottermata"><img src="https://avatars.githubusercontent.com/u/9451844?v=4?s=100" width="100px;" alt="Christopher Krause"/><br /><sub><b>Christopher Krause</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=ottermata" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="http://www.kylesmith.me"><img src="https://avatars.githubusercontent.com/u/1161424?v=4?s=100" width="100px;" alt="Kyle Smith"/><br /><sub><b>Kyle Smith</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=smithk86" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=smithk86" title="Documentation">📖</a> <a href="https://github.com/litestar-org/litestar/issues?q=author%3Asmithk86" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/scott2b"><img src="https://avatars.githubusercontent.com/u/307713?v=4?s=100" width="100px;" alt="Scott Bradley"/><br /><sub><b>Scott Bradley</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/issues?q=author%3Ascott2b" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://www.linkedin.com/in/srikanthccv/"><img src="https://avatars.githubusercontent.com/u/22846633?v=4?s=100" width="100px;" alt="Srikanth Chekuri"/><br /><sub><b>Srikanth Chekuri</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=srikanthccv" title="Tests">⚠️</a> <a href="https://github.com/litestar-org/litestar/commits?author=srikanthccv" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://lonelyviking.com"><img src="https://avatars.githubusercontent.com/u/78952809?v=4?s=100" width="100px;" alt="Michael Bosch"/><br /><sub><b>Michael Bosch</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=LonelyVikingMichael" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/sssssss340"><img src="https://avatars.githubusercontent.com/u/8406195?v=4?s=100" width="100px;" alt="sssssss340"/><br /><sub><b>sssssss340</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/issues?q=author%3Asssssss340" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/ste-pool"><img src="https://avatars.githubusercontent.com/u/17198460?v=4?s=100" width="100px;" alt="ste-pool"/><br /><sub><b>ste-pool</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=ste-pool" title="Code">💻</a> <a href="#infra-ste-pool" title="Infrastructure (Hosting, Build-Tools, etc)">🚇</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/Alc-Alc"><img src="https://avatars.githubusercontent.com/u/45509143?v=4?s=100" width="100px;" alt="Alc-Alc"/><br /><sub><b>Alc-Alc</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=Alc-Alc" title="Documentation">📖</a> <a href="https://github.com/litestar-org/litestar/commits?author=Alc-Alc" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=Alc-Alc" title="Tests">⚠️</a> <a href="#infra-Alc-Alc" title="Infrastructure (Hosting, Build-Tools, etc)">🚇</a></td>
      <td align="center" valign="top" width="14.28%"><a href="http://asomethings.com"><img src="https://avatars.githubusercontent.com/u/16171942?v=4?s=100" width="100px;" alt="asomethings"/><br /><sub><b>asomethings</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=asomethings" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/garburator"><img src="https://avatars.githubusercontent.com/u/14207857?v=4?s=100" width="100px;" alt="Garry Bullock"/><br /><sub><b>Garry Bullock</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=garburator" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/NiclasHaderer"><img src="https://avatars.githubusercontent.com/u/109728711?v=4?s=100" width="100px;" alt="Niclas Haderer"/><br /><sub><b>Niclas Haderer</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=NiclasHaderer" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/dialvarezs"><img src="https://avatars.githubusercontent.com/u/13831919?v=4?s=100" width="100px;" alt="Diego Alvarez"/><br /><sub><b>Diego Alvarez</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=dialvarezs" title="Documentation">📖</a> <a href="https://github.com/litestar-org/litestar/commits?author=dialvarezs" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=dialvarezs" title="Tests">⚠️</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://www.rgare.com"><img src="https://avatars.githubusercontent.com/u/51208317?v=4?s=100" width="100px;" alt="Jason Nance"/><br /><sub><b>Jason Nance</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=rgajason" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/spikenn"><img src="https://avatars.githubusercontent.com/u/32995595?v=4?s=100" width="100px;" alt="Igor Kapadze"/><br /><sub><b>Igor Kapadze</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=spikenn" title="Documentation">📖</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://jarmos.vercel.app"><img src="https://avatars.githubusercontent.com/u/31373860?v=4?s=100" width="100px;" alt="Somraj Saha"/><br /><sub><b>Somraj Saha</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=Jarmos-san" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="http://skulason.me"><img src="https://avatars.githubusercontent.com/u/11139514?v=4?s=100" width="100px;" alt="Magnús Ágúst Skúlason"/><br /><sub><b>Magnús Ágúst Skúlason</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=maggias" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=maggias" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://alessioparma.xyz/"><img src="https://avatars.githubusercontent.com/u/4697032?v=4?s=100" width="100px;" alt="Alessio Parma"/><br /><sub><b>Alessio Parma</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=pomma89" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/Lugoues"><img src="https://avatars.githubusercontent.com/u/372610?v=4?s=100" width="100px;" alt="Peter Brunner"/><br /><sub><b>Peter Brunner</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=Lugoues" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://scriptr.dev/"><img src="https://avatars.githubusercontent.com/u/45884264?v=4?s=100" width="100px;" alt="Jacob Coffee"/><br /><sub><b>Jacob Coffee</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=JacobCoffee" title="Documentation">📖</a> <a href="https://github.com/litestar-org/litestar/commits?author=JacobCoffee" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=JacobCoffee" title="Tests">⚠️</a> <a href="#infra-JacobCoffee" title="Infrastructure (Hosting, Build-Tools, etc)">🚇</a> <a href="#ideas-JacobCoffee" title="Ideas, Planning, & Feedback">🤔</a> <a href="#maintenance-JacobCoffee" title="Maintenance">🚧</a> <a href="#business-JacobCoffee" title="Business development">💼</a> <a href="#design-JacobCoffee" title="Design">🎨</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/Gamazic"><img src="https://avatars.githubusercontent.com/u/33692402?v=4?s=100" width="100px;" alt="Gamazic"/><br /><sub><b>Gamazic</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=Gamazic" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/kareemmahlees"><img src="https://avatars.githubusercontent.com/u/89863279?v=4?s=100" width="100px;" alt="Kareem Mahlees"/><br /><sub><b>Kareem Mahlees</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=kareemmahlees" title="Code">💻</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/abdulhaq-e"><img src="https://avatars.githubusercontent.com/u/2532125?v=4?s=100" width="100px;" alt="Abdulhaq Emhemmed"/><br /><sub><b>Abdulhaq Emhemmed</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=abdulhaq-e" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=abdulhaq-e" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/jenish2014"><img src="https://avatars.githubusercontent.com/u/9599888?v=4?s=100" width="100px;" alt="Jenish"/><br /><sub><b>Jenish</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=jenish2014" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=jenish2014" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/chris-telemetry"><img src="https://avatars.githubusercontent.com/u/78052999?v=4?s=100" width="100px;" alt="chris-telemetry"/><br /><sub><b>chris-telemetry</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=chris-telemetry" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="http://wardpearce.com"><img src="https://avatars.githubusercontent.com/u/27844174?v=4?s=100" width="100px;" alt="Ward"/><br /><sub><b>Ward</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/issues?q=author%3AWardPearce" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://knowsuchagency.com"><img src="https://avatars.githubusercontent.com/u/11974795?v=4?s=100" width="100px;" alt="Stephan Fitzpatrick"/><br /><sub><b>Stephan Fitzpatrick</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/issues?q=author%3Aknowsuchagency" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://codepen.io/ekeric13/"><img src="https://avatars.githubusercontent.com/u/6489651?v=4?s=100" width="100px;" alt="Eric Kennedy"/><br /><sub><b>Eric Kennedy</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=ekeric13" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/wassafshahzad"><img src="https://avatars.githubusercontent.com/u/25094157?v=4?s=100" width="100px;" alt="wassaf shahzad"/><br /><sub><b>wassaf shahzad</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=wassafshahzad" title="Code">💻</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="http://nilsso.github.io"><img src="https://avatars.githubusercontent.com/u/567181?v=4?s=100" width="100px;" alt="Nils Olsson"/><br /><sub><b>Nils Olsson</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=nilsso" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/issues?q=author%3Anilsso" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="14.28%"><a href="http://rileychase.net"><img src="https://avatars.githubusercontent.com/u/1491530?v=4?s=100" width="100px;" alt="Riley Chase"/><br /><sub><b>Riley Chase</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=Nadock" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://gh.arielle.codes"><img src="https://avatars.githubusercontent.com/u/71233171?v=4?s=100" width="100px;" alt="arl"/><br /><sub><b>arl</b></sub></a><br /><a href="#maintenance-onerandomusername" title="Maintenance">🚧</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/Galdanwing"><img src="https://avatars.githubusercontent.com/u/29492757?v=4?s=100" width="100px;" alt="Antoine van der Horst"/><br /><sub><b>Antoine van der Horst</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=Galdanwing" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://nick.groenen.me"><img src="https://avatars.githubusercontent.com/u/145285?v=4?s=100" width="100px;" alt="Nick Groenen"/><br /><sub><b>Nick Groenen</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=zoni" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/giorgiovilardo"><img src="https://avatars.githubusercontent.com/u/56472600?v=4?s=100" width="100px;" alt="Giorgio Vilardo"/><br /><sub><b>Giorgio Vilardo</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=giorgiovilardo" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/bollwyvl"><img src="https://avatars.githubusercontent.com/u/45380?v=4?s=100" width="100px;" alt="Nicholas Bollweg"/><br /><sub><b>Nicholas Bollweg</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=bollwyvl" title="Code">💻</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/tompin82"><img src="https://avatars.githubusercontent.com/u/47041409?v=4?s=100" width="100px;" alt="Tomas Jonsson"/><br /><sub><b>Tomas Jonsson</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=tompin82" title="Tests">⚠️</a> <a href="https://github.com/litestar-org/litestar/commits?author=tompin82" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://www.linkedin.com/in/khiem-doan/"><img src="https://avatars.githubusercontent.com/u/15646249?v=4?s=100" width="100px;" alt="Khiem Doan"/><br /><sub><b>Khiem Doan</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=khiemdoan" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/kedod"><img src="https://avatars.githubusercontent.com/u/35638715?v=4?s=100" width="100px;" alt="kedod"/><br /><sub><b>kedod</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=kedod" title="Documentation">📖</a> <a href="https://github.com/litestar-org/litestar/commits?author=kedod" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=kedod" title="Tests">⚠️</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/sonpro1296"><img src="https://avatars.githubusercontent.com/u/17319142?v=4?s=100" width="100px;" alt="sonpro1296"/><br /><sub><b>sonpro1296</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=sonpro1296" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=sonpro1296" title="Tests">⚠️</a> <a href="#infra-sonpro1296" title="Infrastructure (Hosting, Build-Tools, etc)">🚇</a> <a href="https://github.com/litestar-org/litestar/commits?author=sonpro1296" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://patrickarmengol.com"><img src="https://avatars.githubusercontent.com/u/42473149?v=4?s=100" width="100px;" alt="Patrick Armengol"/><br /><sub><b>Patrick Armengol</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=patrickarmengol" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://sanderwegter.nl"><img src="https://avatars.githubusercontent.com/u/7465799?v=4?s=100" width="100px;" alt="Sander"/><br /><sub><b>Sander</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=SanderWegter" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/erhuabushuo"><img src="https://avatars.githubusercontent.com/u/1642364?v=4?s=100" width="100px;" alt="疯人院主任"/><br /><sub><b>疯人院主任</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=erhuabushuo" title="Documentation">📖</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/aviral-nayya"><img src="https://avatars.githubusercontent.com/u/121891493?v=4?s=100" width="100px;" alt="aviral-nayya"/><br /><sub><b>aviral-nayya</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=aviral-nayya" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/whiskeyriver"><img src="https://avatars.githubusercontent.com/u/162092?v=4?s=100" width="100px;" alt="whiskeyriver"/><br /><sub><b>whiskeyriver</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=whiskeyriver" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://hexcode.tech"><img src="https://avatars.githubusercontent.com/u/419606?v=4?s=100" width="100px;" alt="Phyo Arkar Lwin"/><br /><sub><b>Phyo Arkar Lwin</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=v3ss0n" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/MatthewNewland"><img src="https://avatars.githubusercontent.com/u/9618670?v=4?s=100" width="100px;" alt="MatthewNewland"/><br /><sub><b>MatthewNewland</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/issues?q=author%3AMatthewNewland" title="Bug reports">🐛</a> <a href="https://github.com/litestar-org/litestar/commits?author=MatthewNewland" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=MatthewNewland" title="Tests">⚠️</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/vtarchon"><img src="https://avatars.githubusercontent.com/u/1598170?v=4?s=100" width="100px;" alt="Tom Kuo"/><br /><sub><b>Tom Kuo</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/issues?q=author%3Avtarchon" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/LeckerenSirupwaffeln"><img src="https://avatars.githubusercontent.com/u/83568015?v=4?s=100" width="100px;" alt="LeckerenSirupwaffeln"/><br /><sub><b>LeckerenSirupwaffeln</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/issues?q=author%3ALeckerenSirupwaffeln" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/eldano1995"><img src="https://avatars.githubusercontent.com/u/24553679?v=4?s=100" width="100px;" alt="Daniel González Fernández"/><br /><sub><b>Daniel González Fernández</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=eldano1995" title="Documentation">📖</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/01EK98"><img src="https://avatars.githubusercontent.com/u/101988390?v=4?s=100" width="100px;" alt="01EK98"/><br /><sub><b>01EK98</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=01EK98" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/sarbor"><img src="https://avatars.githubusercontent.com/u/15257226?v=4?s=100" width="100px;" alt="Sarbo Roy"/><br /><sub><b>Sarbo Roy</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=sarbor" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/rseeley"><img src="https://avatars.githubusercontent.com/u/5397221?v=4?s=100" width="100px;" alt="Ryan Seeley"/><br /><sub><b>Ryan Seeley</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=rseeley" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/ctrl-Felix"><img src="https://avatars.githubusercontent.com/u/62290842?v=4?s=100" width="100px;" alt="Felix"/><br /><sub><b>Felix</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=ctrl-Felix" title="Documentation">📖</a> <a href="https://github.com/litestar-org/litestar/issues?q=author%3Actrl-Felix" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://www.linkedin.com/in/gsakkis"><img src="https://avatars.githubusercontent.com/u/291289?v=4?s=100" width="100px;" alt="George Sakkis"/><br /><sub><b>George Sakkis</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=gsakkis" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/floxay"><img src="https://avatars.githubusercontent.com/u/57007485?v=4?s=100" width="100px;" alt="Huba Tuba"/><br /><sub><b>Huba Tuba</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=floxay" title="Documentation">📖</a> <a href="https://github.com/litestar-org/litestar/commits?author=floxay" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=floxay" title="Tests">⚠️</a></td>
      <td align="center" valign="top" width="14.28%"><a href="http://fermigier.com/"><img src="https://avatars.githubusercontent.com/u/271079?v=4?s=100" width="100px;" alt="Stefane Fermigier"/><br /><sub><b>Stefane Fermigier</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=sfermigier" title="Documentation">📖</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/r4gesingh47"><img src="https://avatars.githubusercontent.com/u/71139938?v=4?s=100" width="100px;" alt="r4ge"/><br /><sub><b>r4ge</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=r4gesingh47" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=r4gesingh47" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/jaykv"><img src="https://avatars.githubusercontent.com/u/18240054?v=4?s=100" width="100px;" alt="Jay"/><br /><sub><b>Jay</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=jaykv" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/sinisaos"><img src="https://avatars.githubusercontent.com/u/30960668?v=4?s=100" width="100px;" alt="sinisaos"/><br /><sub><b>sinisaos</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=sinisaos" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/Tsdevendra1"><img src="https://avatars.githubusercontent.com/u/38055748?v=4?s=100" width="100px;" alt="Tharuka Devendra"/><br /><sub><b>Tharuka Devendra</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=Tsdevendra1" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/euri10"><img src="https://avatars.githubusercontent.com/u/1104190?v=4?s=100" width="100px;" alt="euri10"/><br /><sub><b>euri10</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=euri10" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=euri10" title="Documentation">📖</a> <a href="https://github.com/litestar-org/litestar/issues?q=author%3Aeuri10" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/su-shubham"><img src="https://avatars.githubusercontent.com/u/75021117?v=4?s=100" width="100px;" alt="Shubham"/><br /><sub><b>Shubham</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=su-shubham" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://www.linkedin.com/in/erik-hasse"><img src="https://avatars.githubusercontent.com/u/37126755?v=4?s=100" width="100px;" alt="Erik Hasse"/><br /><sub><b>Erik Hasse</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/issues?q=author%3Aerik-hasse" title="Bug reports">🐛</a> <a href="https://github.com/litestar-org/litestar/commits?author=erik-hasse" title="Code">💻</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://sobolevn.me"><img src="https://avatars.githubusercontent.com/u/4660275?v=4?s=100" width="100px;" alt="Nikita Sobolev"/><br /><sub><b>Nikita Sobolev</b></sub></a><br /><a href="#infra-sobolevn" title="Infrastructure (Hosting, Build-Tools, etc)">🚇</a> <a href="https://github.com/litestar-org/litestar/commits?author=sobolevn" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/lazyc97"><img src="https://avatars.githubusercontent.com/u/8538104?v=4?s=100" width="100px;" alt="Nguyễn Hoàng Đức"/><br /><sub><b>Nguyễn Hoàng Đức</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/issues?q=author%3Alazyc97" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/RavanaBhrama"><img src="https://avatars.githubusercontent.com/u/131459969?v=4?s=100" width="100px;" alt="RavanaBhrama"/><br /><sub><b>RavanaBhrama</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=RavanaBhrama" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/mj0nez"><img src="https://avatars.githubusercontent.com/u/20128340?v=4?s=100" width="100px;" alt="Marcel Johannesmann"/><br /><sub><b>Marcel Johannesmann</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=mj0nez" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="http://zanfar.com/"><img src="https://avatars.githubusercontent.com/u/10294685?v=4?s=100" width="100px;" alt="Matthew"/><br /><sub><b>Matthew</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=therealzanfar" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/Mattwmaster58"><img src="https://avatars.githubusercontent.com/u/26337069?v=4?s=100" width="100px;" alt="Mattwmaster58"/><br /><sub><b>Mattwmaster58</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/issues?q=author%3AMattwmaster58" title="Bug reports">🐛</a> <a href="https://github.com/litestar-org/litestar/commits?author=Mattwmaster58" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=Mattwmaster58" title="Tests">⚠️</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://es.linkedin.com/in/manusp"><img src="https://avatars.githubusercontent.com/u/5411704?v=4?s=100" width="100px;" alt="Manuel Sanchez Pinar"/><br /><sub><b>Manuel Sanchez Pinar</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=aorith" title="Documentation">📖</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/juan-riveros"><img src="https://avatars.githubusercontent.com/u/1297567?v=4?s=100" width="100px;" alt="Juan Riveros"/><br /><sub><b>Juan Riveros</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=juan-riveros" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/davidbrochart"><img src="https://avatars.githubusercontent.com/u/4711805?v=4?s=100" width="100px;" alt="David Brochart"/><br /><sub><b>David Brochart</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=davidbrochart" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/sean-donoghue"><img src="https://avatars.githubusercontent.com/u/64597271?v=4?s=100" width="100px;" alt="Sean Donoghue"/><br /><sub><b>Sean Donoghue</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=sean-donoghue" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://sykloid.org/"><img src="https://avatars.githubusercontent.com/u/22753?v=4?s=100" width="100px;" alt="P.C. Shyamshankar"/><br /><sub><b>P.C. Shyamshankar</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/issues?q=author%3Asykloid" title="Bug reports">🐛</a> <a href="https://github.com/litestar-org/litestar/commits?author=sykloid" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=sykloid" title="Tests">⚠️</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/wevonosky"><img src="https://avatars.githubusercontent.com/u/19598171?v=4?s=100" width="100px;" alt="William Evonosky"/><br /><sub><b>William Evonosky</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=wevonosky" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/geeshta"><img src="https://avatars.githubusercontent.com/u/61031243?v=4?s=100" width="100px;" alt="geeshta"/><br /><sub><b>geeshta</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=geeshta" title="Documentation">📖</a> <a href="https://github.com/litestar-org/litestar/commits?author=geeshta" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/issues?q=author%3Ageeshta" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://fosstodon.org/@robertrosca"><img src="https://avatars.githubusercontent.com/u/32569096?v=4?s=100" width="100px;" alt="Robert Rosca"/><br /><sub><b>Robert Rosca</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=RobertRosca" title="Documentation">📖</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/syshenyu"><img src="https://avatars.githubusercontent.com/u/92897003?v=4?s=100" width="100px;" alt="DICE_Lab"/><br /><sub><b>DICE_Lab</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=syshenyu" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/lsanpablo"><img src="https://avatars.githubusercontent.com/u/7145688?v=4?s=100" width="100px;" alt="Luis San Pablo"/><br /><sub><b>Luis San Pablo</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=lsanpablo" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=lsanpablo" title="Tests">⚠️</a> <a href="https://github.com/litestar-org/litestar/commits?author=lsanpablo" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/Lancetnik"><img src="https://avatars.githubusercontent.com/u/44573917?v=4?s=100" width="100px;" alt="Pastukhov Nikita"/><br /><sub><b>Pastukhov Nikita</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=Lancetnik" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="http://jamesoclaire.com"><img src="https://avatars.githubusercontent.com/u/7601451?v=4?s=100" width="100px;" alt="James O'Claire"/><br /><sub><b>James O'Claire</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=ddxv" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/pbaletkeman"><img src="https://avatars.githubusercontent.com/u/22402240?v=4?s=100" width="100px;" alt="Pete"/><br /><sub><b>Pete</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=pbaletkeman" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="http://www.hera.cc"><img src="https://avatars.githubusercontent.com/u/534840?v=4?s=100" width="100px;" alt="Alexandre Richonnier"/><br /><sub><b>Alexandre Richonnier</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=heralight" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=heralight" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/betaboon"><img src="https://avatars.githubusercontent.com/u/7346933?v=4?s=100" width="100px;" alt="betaboon"/><br /><sub><b>betaboon</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=betaboon" title="Code">💻</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/brakhane"><img src="https://avatars.githubusercontent.com/u/541637?v=4?s=100" width="100px;" alt="Dennis Brakhane"/><br /><sub><b>Dennis Brakhane</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=brakhane" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/issues?q=author%3Abrakhane" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://mind.wiki"><img src="https://avatars.githubusercontent.com/u/7423639?v=4?s=100" width="100px;" alt="Pragy Agarwal"/><br /><sub><b>Pragy Agarwal</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=AgarwalPragy" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/dybi"><img src="https://avatars.githubusercontent.com/u/36961162?v=4?s=100" width="100px;" alt="Piotr Dybowski"/><br /><sub><b>Piotr Dybowski</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=dybi" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/myslak71"><img src="https://avatars.githubusercontent.com/u/43068450?v=4?s=100" width="100px;" alt="Konrad Szczurek"/><br /><sub><b>Konrad Szczurek</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=myslak71" title="Documentation">📖</a> <a href="https://github.com/litestar-org/litestar/commits?author=myslak71" title="Tests">⚠️</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/orgarten"><img src="https://avatars.githubusercontent.com/u/10799869?v=4?s=100" width="100px;" alt="Orell Garten"/><br /><sub><b>Orell Garten</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=orgarten" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=orgarten" title="Documentation">📖</a> <a href="https://github.com/litestar-org/litestar/commits?author=orgarten" title="Tests">⚠️</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/Kumzy"><img src="https://avatars.githubusercontent.com/u/5995441?v=4?s=100" width="100px;" alt="Julien"/><br /><sub><b>Julien</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=Kumzy" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/leejayhsu"><img src="https://avatars.githubusercontent.com/u/37034741?v=4?s=100" width="100px;" alt="Leejay Hsu"/><br /><sub><b>Leejay Hsu</b></sub></a><br /><a href="#maintenance-leejayhsu" title="Maintenance">🚧</a> <a href="#infra-leejayhsu" title="Infrastructure (Hosting, Build-Tools, etc)">🚇</a> <a href="https://github.com/litestar-org/litestar/commits?author=leejayhsu" title="Documentation">📖</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://x14.nl"><img src="https://avatars.githubusercontent.com/u/659504?v=4?s=100" width="100px;" alt="Michiel W. Beijen"/><br /><sub><b>Michiel W. Beijen</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=mbeijen" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/baoliay2008"><img src="https://avatars.githubusercontent.com/u/13620348?v=4?s=100" width="100px;" alt="L. Bao"/><br /><sub><b>L. Bao</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=baoliay2008" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="http://jarredglaser.com"><img src="https://avatars.githubusercontent.com/u/32422167?v=4?s=100" width="100px;" alt="Jarred Glaser"/><br /><sub><b>Jarred Glaser</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=jdglaser" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/hunterjsb"><img src="https://avatars.githubusercontent.com/u/69213737?v=4?s=100" width="100px;" alt="Hunter Boyd"/><br /><sub><b>Hunter Boyd</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=hunterjsb" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/cesarmg1980"><img src="https://avatars.githubusercontent.com/u/38872121?v=4?s=100" width="100px;" alt="Cesar Giulietti"/><br /><sub><b>Cesar Giulietti</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=cesarmg1980" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://gitlab.com/marcuslimdw/"><img src="https://avatars.githubusercontent.com/u/42759889?v=4?s=100" width="100px;" alt="Marcus Lim"/><br /><sub><b>Marcus Lim</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=marcuslimdw" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/hzhou0"><img src="https://avatars.githubusercontent.com/u/43188301?v=4?s=100" width="100px;" alt="Henry Zhou"/><br /><sub><b>Henry Zhou</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/issues?q=author%3Ahzhou0" title="Bug reports">🐛</a> <a href="https://github.com/litestar-org/litestar/commits?author=hzhou0" title="Code">💻</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/WilliamStam"><img src="https://avatars.githubusercontent.com/u/182800?v=4?s=100" width="100px;" alt="William Stam"/><br /><sub><b>William Stam</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=WilliamStam" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/andrewdoh"><img src="https://avatars.githubusercontent.com/u/7662358?v=4?s=100" width="100px;" alt="andrew do"/><br /><sub><b>andrew do</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=andrewdoh" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=andrewdoh" title="Tests">⚠️</a> <a href="https://github.com/litestar-org/litestar/commits?author=andrewdoh" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/cbscsm"><img src="https://avatars.githubusercontent.com/u/31615733?v=4?s=100" width="100px;" alt="Boseong Choi"/><br /><sub><b>Boseong Choi</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=cbscsm" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=cbscsm" title="Tests">⚠️</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/wer153"><img src="https://avatars.githubusercontent.com/u/23370765?v=4?s=100" width="100px;" alt="Kim Minki"/><br /><sub><b>Kim Minki</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=wer153" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=wer153" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://velog.io/@azzurri21"><img src="https://avatars.githubusercontent.com/u/86508420?v=4?s=100" width="100px;" alt="Jeongseop Lim"/><br /><sub><b>Jeongseop Lim</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=jseop-lim" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/FergusMok"><img src="https://avatars.githubusercontent.com/u/10182564?v=4?s=100" width="100px;" alt="FergusMok"/><br /><sub><b>FergusMok</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=FergusMok" title="Documentation">📖</a> <a href="https://github.com/litestar-org/litestar/commits?author=FergusMok" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=FergusMok" title="Tests">⚠️</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/manusinghal19"><img src="https://avatars.githubusercontent.com/u/8455587?v=4?s=100" width="100px;" alt="Manu Singhal"/><br /><sub><b>Manu Singhal</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=manusinghal19" title="Documentation">📖</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://cv.ycwu.space"><img src="https://avatars.githubusercontent.com/u/67060418?v=4?s=100" width="100px;" alt="Jerry Wu"/><br /><sub><b>Jerry Wu</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=jrycw" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/horo-fox"><img src="https://avatars.githubusercontent.com/u/143025439?v=4?s=100" width="100px;" alt="horo"/><br /><sub><b>horo</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/issues?q=author%3Ahoro-fox" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/rosstitmarsh"><img src="https://avatars.githubusercontent.com/u/23349806?v=4?s=100" width="100px;" alt="Ross Titmarsh"/><br /><sub><b>Ross Titmarsh</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=rosstitmarsh" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/korneevm"><img src="https://avatars.githubusercontent.com/u/743250?v=4?s=100" width="100px;" alt="Mike Korneev"/><br /><sub><b>Mike Korneev</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=korneevm" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/patrickneise"><img src="https://avatars.githubusercontent.com/u/6312074?v=4?s=100" width="100px;" alt="Patrick Neise"/><br /><sub><b>Patrick Neise</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=patrickneise" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/JeanArhancet"><img src="https://avatars.githubusercontent.com/u/10811879?v=4?s=100" width="100px;" alt="Jean Arhancet"/><br /><sub><b>Jean Arhancet</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/issues?q=author%3AJeanArhancet" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="14.28%"><a href="http://dnquark.com"><img src="https://avatars.githubusercontent.com/u/338250?v=4?s=100" width="100px;" alt="Leo Alekseyev"/><br /><sub><b>Leo Alekseyev</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=betaprior" title="Code">💻</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/aranvir"><img src="https://avatars.githubusercontent.com/u/75439739?v=4?s=100" width="100px;" alt="aranvir"/><br /><sub><b>aranvir</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=aranvir" title="Documentation">📖</a> <a href="https://github.com/litestar-org/litestar/commits?author=aranvir" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=aranvir" title="Tests">⚠️</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/bunny-therapist"><img src="https://avatars.githubusercontent.com/u/87039365?v=4?s=100" width="100px;" alt="bunny-therapist"/><br /><sub><b>bunny-therapist</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=bunny-therapist" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="http://www.benluo.cc"><img src="https://avatars.githubusercontent.com/u/70398?v=4?s=100" width="100px;" alt="Ben Luo"/><br /><sub><b>Ben Luo</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=benluo" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/hugovk"><img src="https://avatars.githubusercontent.com/u/1324225?v=4?s=100" width="100px;" alt="Hugo van Kemenade"/><br /><sub><b>Hugo van Kemenade</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=hugovk" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://error418.github.io"><img src="https://avatars.githubusercontent.com/u/7716544?v=4?s=100" width="100px;" alt="Michael Gerbig"/><br /><sub><b>Michael Gerbig</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=error418" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/crisog"><img src="https://avatars.githubusercontent.com/u/40803711?v=4?s=100" width="100px;" alt="CrisOG"/><br /><sub><b>CrisOG</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/issues?q=author%3Acrisog" title="Bug reports">🐛</a> <a href="https://github.com/litestar-org/litestar/commits?author=crisog" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=crisog" title="Tests">⚠️</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/haryle"><img src="https://avatars.githubusercontent.com/u/64817481?v=4?s=100" width="100px;" alt="harryle"/><br /><sub><b>harryle</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=haryle" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=haryle" title="Tests">⚠️</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="http://www.b-list.org/"><img src="https://avatars.githubusercontent.com/u/12384?v=4?s=100" width="100px;" alt="James Bennett"/><br /><sub><b>James Bennett</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/issues?q=author%3Aubernostrum" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/sherbang"><img src="https://avatars.githubusercontent.com/u/275015?v=4?s=100" width="100px;" alt="sherbang"/><br /><sub><b>sherbang</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=sherbang" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/carlsmedstad"><img src="https://avatars.githubusercontent.com/u/6952324?v=4?s=100" width="100px;" alt="Carl Smedstad"/><br /><sub><b>Carl Smedstad</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=carlsmedstad" title="Tests">⚠️</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/maintain0404"><img src="https://avatars.githubusercontent.com/u/50428534?v=4?s=100" width="100px;" alt="Taein Min"/><br /><sub><b>Taein Min</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=maintain0404" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/wallseat"><img src="https://avatars.githubusercontent.com/u/26143672?v=4?s=100" width="100px;" alt="Stanislav Lyu."/><br /><sub><b>Stanislav Lyu.</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/issues?q=author%3Awallseat" title="Bug reports">🐛</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/tibor-reiss"><img src="https://avatars.githubusercontent.com/u/75096465?v=4?s=100" width="100px;" alt="Tibor Reiss"/><br /><sub><b>Tibor Reiss</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=tibor-reiss" title="Tests">⚠️</a> <a href="https://github.com/litestar-org/litestar/commits?author=tibor-reiss" title="Documentation">📖</a> <a href="https://github.com/litestar-org/litestar/commits?author=tibor-reiss" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://pogrom.dev"><img src="https://avatars.githubusercontent.com/u/11032969?v=4?s=100" width="100px;" alt="Alex"/><br /><sub><b>Alex</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/issues?q=author%3A0xE111" title="Bug reports">🐛</a> <a href="https://github.com/litestar-org/litestar/commits?author=0xE111" title="Code">💻</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="http://0110.be"><img src="https://avatars.githubusercontent.com/u/60453?v=4?s=100" width="100px;" alt="Joren Six"/><br /><sub><b>Joren Six</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=JorenSix" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/jderrien"><img src="https://avatars.githubusercontent.com/u/145396?v=4?s=100" width="100px;" alt="jderrien"/><br /><sub><b>jderrien</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=jderrien" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://possiblepanda.me"><img src="https://avatars.githubusercontent.com/u/85448494?v=4?s=100" width="100px;" alt="PossiblePanda"/><br /><sub><b>PossiblePanda</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=PossiblePanda" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/evstratbg"><img src="https://avatars.githubusercontent.com/u/10176401?v=4?s=100" width="100px;" alt="evstrat"/><br /><sub><b>evstrat</b></sub></a><br /><a href="#infra-evstratbg" title="Infrastructure (Hosting, Build-Tools, etc)">🚇</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://speakerdeck.com/eltociear"><img src="https://avatars.githubusercontent.com/u/22633385?v=4?s=100" width="100px;" alt="Ikko Eltociear Ashimine"/><br /><sub><b>Ikko Eltociear Ashimine</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=eltociear" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/taihim"><img src="https://avatars.githubusercontent.com/u/13764071?v=4?s=100" width="100px;" alt="Taimur Ibrahim"/><br /><sub><b>Taimur Ibrahim</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=taihim" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/l-armstrong"><img src="https://avatars.githubusercontent.com/u/43922258?v=4?s=100" width="100px;" alt="l-armstrong"/><br /><sub><b>l-armstrong</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=l-armstrong" title="Documentation">📖</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/Anu-cool-007"><img src="https://avatars.githubusercontent.com/u/16525919?v=4?s=100" width="100px;" alt="Anuranjan Srivastava"/><br /><sub><b>Anuranjan Srivastava</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=Anu-cool-007" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/Zimzozaur"><img src="https://avatars.githubusercontent.com/u/106471045?v=4?s=100" width="100px;" alt="Simon Joseph"/><br /><sub><b>Simon Joseph</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=Zimzozaur" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/abelkm99"><img src="https://avatars.githubusercontent.com/u/41730180?v=4?s=100" width="100px;" alt="Abel Kidanemariam"/><br /><sub><b>Abel Kidanemariam</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=abelkm99" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=abelkm99" title="Tests">⚠️</a> <a href="https://github.com/litestar-org/litestar/commits?author=abelkm99" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://blog.trim21.me/"><img src="https://avatars.githubusercontent.com/u/13553903?v=4?s=100" width="100px;" alt="Trim21"/><br /><sub><b>Trim21</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=trim21" title="Code">💻</a> <a href="https://github.com/litestar-org/litestar/commits?author=trim21" title="Tests">⚠️</a></td>
      <td align="center" valign="top" width="14.28%"><a href="http://aarcex3.github.io"><img src="https://avatars.githubusercontent.com/u/59893355?v=4?s=100" width="100px;" alt="Agustin Arce"/><br /><sub><b>Agustin Arce</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=aarcex3" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/FarhanAliRaza"><img src="https://avatars.githubusercontent.com/u/62690310?v=4?s=100" width="100px;" alt="Farhan Ali Raza"/><br /><sub><b>Farhan Ali Raza</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=FarhanAliRaza" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/pogopaule"><img src="https://avatars.githubusercontent.com/u/576949?v=4?s=100" width="100px;" alt="Fabian"/><br /><sub><b>Fabian</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=pogopaule" title="Code">💻</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/mohammedbabelly20"><img src="https://avatars.githubusercontent.com/u/104768048?v=4?s=100" width="100px;" alt="Mohammed Babelly"/><br /><sub><b>Mohammed Babelly</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=mohammedbabelly20" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://keybase.io/charlesdyfisnet"><img src="https://avatars.githubusercontent.com/u/22370?v=4?s=100" width="100px;" alt="Charles Duffy"/><br /><sub><b>Charles Duffy</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=charles-dyfis-net" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/RenameMe1"><img src="https://avatars.githubusercontent.com/u/165988121?v=4?s=100" width="100px;" alt="Evgeny Demchenko"/><br /><sub><b>Evgeny Demchenko</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=RenameMe1" title="Documentation">📖</a> <a href="https://github.com/litestar-org/litestar/commits?author=RenameMe1" title="Tests">⚠️</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://olzhasar.com"><img src="https://avatars.githubusercontent.com/u/12471703?v=4?s=100" width="100px;" alt="Olzhas Arystanov"/><br /><sub><b>Olzhas Arystanov</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/issues?q=author%3Aolzhasar" title="Bug reports">🐛</a> <a href="https://github.com/litestar-org/litestar/commits?author=olzhasar" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/vikigenius"><img src="https://avatars.githubusercontent.com/u/12724810?v=4?s=100" width="100px;" alt="Vikash"/><br /><sub><b>Vikash</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=vikigenius" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/ftsartek"><img src="https://avatars.githubusercontent.com/u/20253317?v=4?s=100" width="100px;" alt="Jordan Russell"/><br /><sub><b>Jordan Russell</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=ftsartek" title="Documentation">📖</a> <a href="https://github.com/litestar-org/litestar/commits?author=ftsartek" title="Tests">⚠️</a> <a href="https://github.com/litestar-org/litestar/commits?author=ftsartek" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://stevenloria.com"><img src="https://avatars.githubusercontent.com/u/2379650?v=4?s=100" width="100px;" alt="Steven Loria"/><br /><sub><b>Steven Loria</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=sloria" title="Documentation">📖</a></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/oek1ng"><img src="https://avatars.githubusercontent.com/u/193062679?v=4?s=100" width="100px;" alt="oek1ng"/><br /><sub><b>oek1ng</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=oek1ng" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/Ada-lave"><img src="https://avatars.githubusercontent.com/u/113159483?v=4?s=100" width="100px;" alt="Vladislav"/><br /><sub><b>Vladislav</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=Ada-lave" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://gaitenis.id.lv"><img src="https://avatars.githubusercontent.com/u/9976861?v=4?s=100" width="100px;" alt="Edgars"/><br /><sub><b>Edgars</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=eandersons" title="Documentation">📖</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://jannchie.com"><img src="https://avatars.githubusercontent.com/u/29743310?v=4?s=100" width="100px;" alt="Jianqi Pan"/><br /><sub><b>Jianqi Pan</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=Jannchie" title="Code">💻</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/PokkaKiyo"><img src="https://avatars.githubusercontent.com/u/31039465?v=4?s=100" width="100px;" alt="PokkaKiyo"/><br /><sub><b>PokkaKiyo</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=PokkaKiyo" title="Tests">⚠️</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/s-aleshin"><img src="https://avatars.githubusercontent.com/u/66841202?v=4?s=100" width="100px;" alt="Sergei Aleshin"/><br /><sub><b>Sergei Aleshin</b></sub></a><br /><a href="https://github.com/litestar-org/litestar/commits?author=s-aleshin" title="Code">💻</a></td>
    </tr>
  </tbody>
</table>

<!-- markdownlint-restore -->
<!-- prettier-ignore-end -->

<!-- ALL-CONTRIBUTORS-LIST:END -->

This project follows the [all-contributors](https://github.com/all-contributors/all-contributors) specification.
Contributions of any kind welcome!

</details>

<!-- contributors-end -->
