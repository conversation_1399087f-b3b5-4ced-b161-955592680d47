default_language_version:
  python: "3.12"
repos:
  - repo: https://github.com/compilerla/conventional-pre-commit
    rev: v4.0.0
    hooks:
      - id: conventional-pre-commit
        stages: [commit-msg]
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: check-ast
      - id: check-case-conflict
      - id: check-toml
      - id: debug-statements
        exclude: ^(litestar/config/app\.py|litestar/app\.py|test_apps/debugging/main\.py)$
      - id: end-of-file-fixer
      - id: mixed-line-ending
      - id: trailing-whitespace
        exclude: "tests/unit/test_openapi/test_typescript_converter/test_converter.py"
  - repo: https://github.com/provinzkraut/unasyncd
    rev: "v0.8.1"
    hooks:
      - id: unasyncd
        additional_dependencies: ["ruff"]
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: "v0.12.8"
    hooks:
      - id: ruff
        args: ["--fix"]
      - id: ruff-format
  - repo: https://github.com/crate-ci/typos
    rev: v1.30.3
    hooks:
      - id: typos
        exclude: "docs/_static"
  - repo: https://github.com/python-formate/flake8-dunder-all
    rev: v0.4.1
    hooks:
      - id: ensure-dunder-all
        exclude: "test*|examples*|tools"
        args: ["--use-tuple"]
  - repo: https://github.com/sphinx-contrib/sphinx-lint
    rev: "v1.0.0"
    hooks:
      - id: sphinx-lint
  - repo: local
    hooks:
      - id: pypi-readme
        name: pypi-readme
        language: python
        entry: python tools/pypi_readme.py
        types: [markdown]
