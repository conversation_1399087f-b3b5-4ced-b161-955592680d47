version: "3"

services:
  postgres:
    image: postgres:latest
    ports:
      - "5423:5432" # use a non-standard port here
    environment:
      POSTGRES_PASSWORD: super-secret
  redis:
    image: redis:latest
    restart: always
    ports:
      - "6397:6379" # use a non-standard port here
  valkey:
    image: valkey/valkey:latest
    restart: always
    ports:
      - "6381:6379" # also a non-standard port
