from litestar.contrib.jwt.jwt_auth import (
    BaseJ<PERSON><PERSON><PERSON>,
    J<PERSON><PERSON>uth,
    J<PERSON><PERSON>ook<PERSON><PERSON>uth,
    OAuth2Login,
    OAuth2PasswordBearerAuth,
)
from litestar.contrib.jwt.jwt_token import Token
from litestar.contrib.jwt.middleware import (
    JWTAuthenticationMiddleware,
    JWTCookieAuthenticationMiddleware,
)
from litestar.utils import warn_deprecation

__all__ = (
    "BaseJWTAuth",
    "JWTA<PERSON>",
    "JWTAuthenticationMiddleware",
    "JWTCookieAuth",
    "JWTCookieAuthenticationMiddleware",
    "OAuth2Login",
    "OAuth2PasswordBearerAuth",
    "Token",
)

warn_deprecation(
    deprecated_name="litestar.contrib.jwt",
    version="2.3.2",
    kind="import",
    removal_in="3.0",
    info="importing from 'litestar.contrib.jwt' is deprecated, please import from 'litestar.security.jwt' instead",
)
