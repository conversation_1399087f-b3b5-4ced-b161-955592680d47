<!-- markdownlint-disable -->
<p align="center">
  <img src="https://raw.githubusercontent.com/litestar-org/branding/473f54621e55cde9acbb6fcab7fc03036173eb3d/assets/Branding%20-%20PNG%20-%20Transparent/Logo%20-%20Banner%20-%20Inline%20-%20Light.png" alt="Litestar Logo - Light" width="100%" height="auto" />
</p>
<!-- markdownlint-restore -->

<div align="center">

<!-- prettier-ignore-start -->

| Project   |     | Status                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|-----------|:----|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| CI/CD     |     | [![Latest Release](https://github.com/litestar-org/litestar/actions/workflows/publish.yml/badge.svg)](https://github.com/litestar-org/litestar/actions/workflows/publish.yml) [![ci](https://github.com/litestar-org/litestar/actions/workflows/ci.yml/badge.svg)](https://github.com/litestar-org/litestar/actions/workflows/ci.yml) [![Documentation Building](https://github.com/litestar-org/litestar/actions/workflows/docs.yml/badge.svg?branch=main)](https://github.com/litestar-org/litestar/actions/workflows/docs.yml)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| Quality   |     | [![Coverage](https://codecov.io/github/litestar-org/litestar/graph/badge.svg?token=vKez4Pycrc)](https://codecov.io/github/litestar-org/litestar)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| Package   |     | [![PyPI - Version](https://img.shields.io/pypi/v/litestar?labelColor=202235&color=edb641&logo=python&logoColor=edb641)](https://badge.fury.io/py/litestar) ![PyPI - Support Python Versions](https://img.shields.io/pypi/pyversions/litestar?labelColor=202235&color=edb641&logo=python&logoColor=edb641) ![Starlite PyPI - Downloads](https://img.shields.io/pypi/dm/starlite?logo=python&label=starlite%20downloads&labelColor=202235&color=edb641&logoColor=edb641) ![Litestar PyPI - Downloads](https://img.shields.io/pypi/dm/litestar?logo=python&label=litestar%20downloads&labelColor=202235&color=edb641&logoColor=edb641)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| Community |     | [![Reddit](https://img.shields.io/reddit/subreddit-subscribers/litestarapi?label=r%2FLitestar&logo=reddit&labelColor=202235&color=edb641&logoColor=edb641)](https://reddit.com/r/litestarapi) [![Discord](https://img.shields.io/discord/919193495116337154?labelColor=202235&color=edb641&label=chat%20on%20discord&logo=discord&logoColor=edb641)](https://discord.gg/litestar) [![Matrix](https://img.shields.io/badge/chat%20on%20Matrix-bridged-202235?labelColor=202235&color=edb641&logo=matrix&logoColor=edb641)](https://matrix.to/#/#litestar:matrix.org) [![Medium](https://img.shields.io/badge/Medium-202235?labelColor=202235&color=edb641&logo=medium&logoColor=edb641)](https://blog.litestar.dev) [![Twitter](https://img.shields.io/twitter/follow/LitestarAPI?labelColor=202235&color=edb641&logo=twitter&logoColor=edb641&style=flat)](https://twitter.com/LitestarAPI) [![Blog](https://img.shields.io/badge/Blog-litestar.dev-202235?logo=blogger&labelColor=202235&color=edb641&logoColor=edb641)](https://blog.litestar.dev)                                                                                                                                                                                                                          |
| Meta      |     | [![Litestar Project](https://img.shields.io/badge/Litestar%20Org-%E2%AD%90%20Litestar-202235.svg?logo=python&labelColor=202235&color=edb641&logoColor=edb641)](https://github.com/litestar-org/litestar) [![types - Mypy](https://img.shields.io/badge/types-Mypy-202235.svg?logo=python&labelColor=202235&color=edb641&logoColor=edb641)](https://github.com/python/mypy) [![License - MIT](https://img.shields.io/badge/license-MIT-202235.svg?logo=python&labelColor=202235&color=edb641&logoColor=edb641)](https://spdx.org/licenses/) [![Litestar Sponsors](https://img.shields.io/badge/Sponsor-%E2%9D%A4-%23edb641.svg?&logo=github&logoColor=edb641&labelColor=202235)](https://github.com/sponsors/litestar-org) [![linting - Ruff](https://img.shields.io/endpoint?url=https://raw.githubusercontent.com/charliermarsh/ruff/main/assets/badge/v2.json&labelColor=202235)](https://github.com/astral-sh/ruff) [![code style - Ruff](https://img.shields.io/endpoint?url=https://raw.githubusercontent.com/astral-sh/ruff/main/assets/badge/format.json&labelColor=202235)](https://github.com/psf/black) [![All Contributors](https://img.shields.io/github/all-contributors/litestar-org/litestar?labelColor=202235&color=edb641&logoColor=edb641)](#contributors-) |

<!-- prettier-ignore-end -->
</div>

<hr>

Litestar is a powerful, flexible yet opinionated ASGI framework, focused on
building APIs. It offers high-performance data validation, dependency injection,
first-class ORM integration, authorization primitives, a rich plugin API, middleware,
and much more that's needed to get applications up and running.

Check out the [documentation 📚](https://docs.litestar.dev/) for a detailed overview of
its features!

Additionally, the [Litestar fullstack repository](https://github.com/litestar-org/litestar-fullstack)
can give you a good impression how a fully fledged Litestar application may look.

<details>
<summary>Table of Contents</summary>

- [Installation](#installation)
  - [Quick Start](#quick-start)
- [Core Features](#core-features)
  - [Example Applications](#example-applications)
- [Features](#features)
  - [Class-based Controllers](#class-based-controllers)
  - [Data Parsing, Type Hints, and Msgspec](#data-parsing-type-hints-and-msgspec)
  - [Plugin System, ORM support, and DTOs](#plugin-system-orm-support-and-dtos)
  - [OpenAPI](#openapi)
  - [Dependency Injection](#dependency-injection)
  - [Middleware](#middleware)
  - [Route Guards](#route-guards)
  - [Request Life Cycle Hooks](#request-life-cycle-hooks)
- [Performance](#performance)
- [Contributing](#contributing)

</details>

## Installation

```shell
pip install litestar
```
or to include the CLI and a server (uvicorn) for running your application:
```shell
pip install 'litestar[standard]'
```

## Quick Start

```python title="app.py"
from litestar import Litestar, get

@get("/")
async def hello_world() -> dict[str, str]:
    """Keeping the tradition alive with hello world."""
    return {"hello": "world"}

app = Litestar(route_handlers=[hello_world])
```

And run it with

```bash
litestar run
```


## Core Features

- [Class based controllers](#class-based-controllers)
- [Dependency Injection](#dependency-injection)
- [Layered Middleware](#middleware)
- [Plugin System](#plugin-system-orm-support-and-dtos)
- [OpenAPI 3.1 schema generation](#openapi)
- [Life Cycle Hooks](#request-life-cycle-hooks)
- [Route Guards based Authorization](#route-guards)
- Support for `dataclasses`, `TypedDict`, [`msgspec`](https://jcristharif.com/msgspec/), [pydantic version 1 and version 2 (even within the same application)](https://docs.pydantic.dev/latest/) and [(c)attrs](https://catt.rs/en/stable/)
  [msgspec](https://github.com/jcrist/msgspec) and [attrs](https://www.attrs.org/en/stable/)
- Layered parameter declaration
- Support for [RFC 9457](https://datatracker.ietf.org/doc/html/rfc9457) standardized "Problem Detail" error responses
- [Automatic API documentation with](#redoc-swagger-ui-and-stoplight-elements-api-documentation):
  - [Scalar](https://github.com/scalar/scalar/)
  - [RapiDoc](https://github.com/rapi-doc/RapiDoc)
  - [Redoc](https://github.com/Redocly/redoc)
  - [Stoplight Elements](https://github.com/stoplightio/elements)
  - [Swagger-UI](https://swagger.io/tools/swagger-ui/)
- [Trio](https://trio.readthedocs.io/en/stable/) support (built-in, via [AnyIO](https://anyio.readthedocs.io/))
- Ultra-fast validation, serialization and deserialization using [msgspec](https://github.com/jcrist/msgspec)
- [SQLAlchemy integration](https://docs.advanced-alchemy.litestar.dev/latest/)

## Example Applications

<details>
<summary>Pre-built Example Apps</summary>

- [litestar-hello-world](https://github.com/litestar-org/litestar-hello-world): A bare-minimum application setup. Great
for testing and POC work.
- [litestar-fullstack](https://github.com/litestar-org/litestar-fullstack): A reference application that contains most of the boilerplate required for a web application.
  It features a Litestar app configured with best practices, SQLAlchemy 2.0 and SAQ, a frontend integrated with Vitejs and Jinja2 templates, Docker, and more. Like all
  Litestar projects, this application is open to contributions, big and small.
</details>

## Sponsors

Litestar is an open-source project, and we enjoy the support of our sponsors to help fund the exciting
work we do.

A **huge** thanks to our sponsors:

[//]: # "Note to maintainers: Highest sponsors first; no more than 3 per row - create new div if needed"

<a href="https://github.com/scalar/scalar/?utm_source=litestar&utm_medium=website&utm_campaign=main-badge" target="_blank" title="Scalar.com - Document, Discover and Test APIs with Scalar."><img src="https://raw.githubusercontent.com/litestar-org/branding/main/assets/sponsors/scalar.svg" width="180" alt="Scalar.com"></a>
<a href="https://telemetrysports.com/" title="Telemetry Sports - Changing the way data influences the sports experience"><img src="https://raw.githubusercontent.com/litestar-org/branding/main/assets/sponsors/telemetry-sports/unofficial-telemetry-whitebg.svg" width="150" alt="Telemetry Sports"></a>

<a href="https://docs.litestar.dev/dev/#sponsors" class="external-link" target="_blank">Check out our sponsors in the docs</a>

If you would like to support the work that we do please consider [becoming a sponsor][sponsor-polar]
via [Polar.sh][sponsor-polar] (preferred), [GitHub][sponsor-github] or [Open Collective][sponsor-oc].

Also, exclusively with [Polar][sponsor-polar], you can engage in pledge-based sponsorships.

[sponsor-github]: https://github.com/sponsors/litestar-org
[sponsor-oc]: https://opencollective.com/litestar
[sponsor-polar]: https://polar.sh/litestar-org

## Features

### Class-based Controllers

While supporting function-based route handlers, Litestar also supports and promotes python OOP using class based
controllers:

<details>
<summary>Example for class-based controllers</summary>

```python title="my_app/controllers/user.py"
from typing import List, Optional
from datetime import datetime

from litestar import Controller, get, post, put, patch, delete
from litestar.dto import DTOData
from pydantic import UUID4

from my_app.models import User, PartialUserDTO


class UserController(Controller):
    path = "/users"

    @post()
    async def create_user(self, data: User) -> User: ...

    @get()
    async def list_users(self) -> List[User]: ...

    @get(path="/{date:int}")
    async def list_new_users(self, date: datetime) -> List[User]: ...

    @patch(path="/{user_id:uuid}", dto=PartialUserDTO)
    async def partial_update_user(
        self, user_id: UUID4, data: DTOData[PartialUserDTO]
    ) -> User: ...

    @put(path="/{user_id:uuid}")
    async def update_user(self, user_id: UUID4, data: User) -> User: ...

    @get(path="/{user_name:str}")
    async def get_user_by_name(self, user_name: str) -> Optional[User]: ...

    @get(path="/{user_id:uuid}")
    async def get_user(self, user_id: UUID4) -> User: ...

    @delete(path="/{user_id:uuid}")
    async def delete_user(self, user_id: UUID4) -> None: ...
```

</details>

### Data Parsing, Type Hints, and Msgspec

Litestar is rigorously typed, and it enforces typing. For example, if you forget to type a return value for a route
handler, an exception will be raised. The reason for this is that Litestar uses typing data to generate OpenAPI specs,
as well as to validate and parse data. Thus, typing is essential to the framework.

Furthermore, Litestar allows extending its support using plugins.

### Plugin System, ORM support, and DTOs

Litestar has a plugin system that allows the user to extend serialization/deserialization, OpenAPI generation, and other
features.

It ships with a builtin plugin for SQL Alchemy, which allows the user to use SQLAlchemy declarative classes "natively"
i.e., as type parameters that will be serialized/deserialized and to return them as values from route
handlers.

Litestar also supports the programmatic creation of DTOs with a `DTOFactory` class, which also supports the use of
plugins.

### OpenAPI

Litestar has custom logic to generate OpenAPI 3.1.0 schema, include optional generation of examples using the
[`polyfactory`](https://pypi.org/project/polyfactory/) library.

#### ReDoc, Swagger-UI and Stoplight Elements API Documentation

Litestar serves the documentation from the generated OpenAPI schema with:

- [ReDoc](https://redoc.ly/)
- [Swagger-UI](https://swagger.io/tools/swagger-ui/)
- [Stoplight Elements](https://github.com/stoplightio/elements)
- [RapiDoc](https://rapidocweb.com/)

All these are available and enabled by default.

### Dependency Injection

Litestar has a simple but powerful DI system inspired by pytest. You can define named dependencies - sync or async - at
different levels of the application, and then selective use or overwrite them.

<details>
<summary>Example for DI</summary>

```python
from litestar import Litestar, get
from litestar.di import Provide


async def my_dependency() -> str: ...


@get("/")
async def index(injected: str) -> str:
    return injected


app = Litestar([index], dependencies={"injected": Provide(my_dependency)})
```

</details>

### Middleware

Litestar supports typical ASGI middleware and ships with middlewares to handle things such as

- CORS
- CSRF
- Rate limiting
- GZip and Brotli compression
- Client- and server-side sessions

### Route Guards

Litestar has an authorization mechanism called `guards`, which allows the user to define guard functions at different
level of the application (app, router, controller etc.) and validate the request before hitting the route handler
function.

<details>
<summary>Example for route guards</summary>

```python
from litestar import Litestar, get

from litestar.connection import ASGIConnection
from litestar.handlers.base import BaseRouteHandler
from litestar.exceptions import NotAuthorizedException


async def is_authorized(connection: ASGIConnection, handler: BaseRouteHandler) -> None:
    # validate authorization
    # if not authorized, raise NotAuthorizedException
    raise NotAuthorizedException()


@get("/", guards=[is_authorized])
async def index() -> None: ...


app = Litestar([index])
```

</details>

### Request Life Cycle Hooks

Litestar supports request life cycle hooks, similarly to Flask - i.e. `before_request` and `after_request`

## Performance

Litestar is fast. It is on par with, or significantly faster than comparable ASGI frameworks.

You can see and run the benchmarks [here](https://github.com/litestar-org/api-performance-tests),
or read more about it [here](https://docs.litestar.dev/latest/benchmarks) in our documentation.

## Contributing

Litestar is open to contributions big and small. You can always [join our discord](https://discord.gg/litestar) server
or [join our Matrix](https://matrix.to/#/#litestar:matrix.org) space
to discuss contributions and project maintenance. For guidelines on how to contribute, please
see [the contribution guide](CONTRIBUTING.rst).
