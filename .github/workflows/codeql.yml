name: CodeQL scheduled
on:
  schedule:
    - cron: "0 4 * * *"
jobs:
  codeql:
    runs-on: ubuntu-latest
    permissions:
      security-events: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v5
        with:
          ref: "main"
      - name: Initialize CodeQL With Dependencies
        uses: github/codeql-action/init@v3
      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
