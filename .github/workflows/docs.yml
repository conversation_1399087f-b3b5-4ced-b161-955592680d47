name: Documentation Building

on:
  release:
    types: [published]
  push:
    branches:
      - main
      - v2
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to build docs for (1, 2, main, or release)'
        required: true
        default: 'main'
        type: choice
        options:
          - main
          - '2'
          - '1'
          - release

env:
  UV_LOCKED: 1

jobs:
  docs:
    permissions:
      contents: write
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5

      - uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install uv
        uses: astral-sh/setup-uv@v6
        with:
          version: "0.5.4"
          enable-cache: true

      - name: Install dependencies
        run: uv sync

      - name: Fetch gh pages
        run: git fetch origin gh-pages --depth=1

      - name: Build release docs
        run: uv run python tools/build_docs.py docs-build
        if: github.event_name == 'release'

      - name: Build docs (main branch)
        run: uv run python tools/build_docs.py docs-build --version main
        if: github.event_name == 'push' && github.ref == 'refs/heads/main'

      - name: Build docs (v2 branch)
        run: uv run python tools/build_docs.py docs-build --version 2
        if: github.event_name == 'push' && github.ref == 'refs/heads/v2'

      - name: Build docs (manual trigger)
        run: |
          if [ "${{ inputs.version }}" == "release" ]; then
            uv run python tools/build_docs.py docs-build
          else
            uv run python tools/build_docs.py docs-build --version ${{ inputs.version }}
          fi
        if: github.event_name == 'workflow_dispatch'

      - name: Deploy
        uses: JamesIves/github-pages-deploy-action@v4
        with:
          folder: docs-build
