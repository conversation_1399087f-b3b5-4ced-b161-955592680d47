name: Latest Release

on:
  release:
    types: [published]
  workflow_dispatch:

jobs:

  publish-release:
    name: upload release to PyPI
    runs-on: ubuntu-latest
    permissions:
      id-token: write
    environment: release
    steps:
      - name: Check out repository
        uses: actions/checkout@v5

      - uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install uv
        uses: astral-sh/setup-uv@v6
        with:
          version: "0.5.4"
          enable-cache: true

      - name: Build package
        run: uv build

      - name: Publish package distributions to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1

  notify-issues:
    needs: publish-release
    name: Notify issues
    uses: ./.github/workflows/notify-released-issues.yml
    with:
      release_tag: ${{ github.event.release.tag_name }}
