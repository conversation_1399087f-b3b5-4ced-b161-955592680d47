name: "Pull Request Labeler"

on:
  pull_request_target:

jobs:
  apply-labels:
    permissions:
      contents: read
      pull-requests: write
      checks: write
      statuses: write
    runs-on: ubuntu-latest
    steps:
    - uses: fuxingloh/multi-labeler@v4
      with:
        github-token: "${{ secrets.GITHUB_TOKEN }}"

  distinguish-pr-origin:
    needs: apply-labels
    if: ${{ always() }}
    permissions:
      pull-requests: write
    runs-on: ubuntu-latest
    steps:
      - uses: actions/github-script@v7
        with:
          github-token: ${{secrets.GITHUB_TOKEN}}
          script: |
            const maintainers = [
              'JacobCoffee', 'provinzkraut', 'cofin',
              'peterschutt', 'Alc-Alc', 'guacs',
              'dependabot[bot]', 'all-contributors[bot]'
            ]
            if (maintainers.includes(context.payload.sender.login)) {
              github.rest.issues.addLabels({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                labels: ['pr/internal']
              })
            } else {
              github.rest.issues.addLabels({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                labels: ['pr/external', 'Triage Required :hospital:']
              })
            }
