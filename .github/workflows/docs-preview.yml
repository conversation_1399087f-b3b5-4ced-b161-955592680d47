name: Deploy documentation preview
on:
  workflow_run:
    workflows: [Tests And Linting]
    types: [completed]

jobs:
  deploy:
    if: ${{ github.event.workflow_run.conclusion == 'success' &&  github.event.workflow_run.event == 'pull_request' }}
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write

    steps:
      - name: Check out repository
        uses: actions/checkout@v5

      - name: Download artifact
        uses: dawidd6/action-download-artifact@v11
        with:
          workflow_conclusion: success
          run_id: ${{ github.event.workflow_run.id }}
          path: docs-preview
          name: docs-preview

      - name: Validate and set PR number
        run: |
          PR_NUMBER=$(cat docs-preview/.pr_number)
          if ! [[ "$PR_NUMBER" =~ ^[0-9]+$ ]]; then
            echo "Invalid PR number: $PR_NUMBER"
            exit 1
          fi
          echo "PR_NUMBER=$PR_NUMBER" >> $GITHUB_ENV

      - name: Deploy docs preview
        uses: JamesIves/github-pages-deploy-action@v4
        with:
          folder: docs-preview/docs/_build/html
          token: ${{ secrets.DOCS_PREVIEW_DEPLOY_TOKEN }}
          repository-name: litestar-org/litestar-docs-preview
          clean: false
          target-folder: ${{ env.PR_NUMBER }}
          branch: gh-pages

      - uses: actions/github-script@v7
        env:
          PR_NUMBER: ${{ env.PR_NUMBER }}
        with:
          script: |
            const issue_number = process.env.PR_NUMBER
            const body = "Documentation preview will be available shortly at https://litestar-org.github.io/litestar-docs-preview/" + issue_number

            const opts = github.rest.issues.listComments.endpoint.merge({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: issue_number,
            });

            const comments = await github.paginate(opts)

            for (const comment of comments) {
              if (comment.user.id === 41898282 && comment.body === body) {
                await github.rest.issues.deleteComment({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  comment_id: comment.id
                })
              }
            }

            await github.rest.issues.createComment({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  issue_number: issue_number,
                  body: body,
            })
