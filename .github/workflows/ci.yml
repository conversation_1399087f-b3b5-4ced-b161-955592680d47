name: Tests And Linting

on:
  pull_request:
  merge_group:
  push:
    branches:
      - main

env:
  UV_LOCKED: 1

jobs:
  validate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5

      - uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install Pre-Commit
        run: python -m pip install pre-commit

      - name: Load cached Pre-Commit Dependencies
        id: cached-pre-commit-dependencies
        uses: actions/cache@v4
        with:
          path: ~/.cache/pre-commit/
          key: pre-commit-4|${{ env.pythonLocation }}|${{ hashFiles('.pre-commit-config.yaml') }}

      - name: Execute Pre-Commit
        run: pre-commit run --show-diff-on-failure --color=always --all-files

  mypy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5

      - uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install uv
        uses: astral-sh/setup-uv@v6
        with:
          enable-cache: true

      - name: Run mypy
        run: uv run mypy

  pyright:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5

      - uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install uv
        uses: astral-sh/setup-uv@v6
        with:
          enable-cache: true

      - name: Run pyright
        run: uv run pyright

  slotscheck:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5

      - uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install uv
        uses: astral-sh/setup-uv@v6
        with:
          enable-cache: true

      - name: Run slotscheck
        run: uv run slotscheck litestar

  test:
    name: "test (${{ matrix.python-version }})"
    strategy:
      fail-fast: true
      matrix:
        python-version: ["3.9", "3.10", "3.11", "3.12", "3.13"]
    uses: ./.github/workflows/test.yml
    with:
      coverage: ${{ (matrix.python-version == '3.12' || matrix.python-version == '3.9') }}
      python-version: ${{ matrix.python-version }}

  # add an aggregate step here to check if any of the steps of the matrix 'test' job
  # failed. this allows us to have dynamic or diverging steps in the matrix, while still
  # being able to mark the 'test' step as a required check for a PR to be considered
  # mergeable, without having to specify each individual matrix item.
  test_success:
    needs: test
    # ensure this step always runs
    if: always()
    runs-on: ubuntu-latest
    steps:
      - name: Report success or fail
        run: exit ${{ needs.test.result == 'success' && '0' || '1' }}

  test_typing_extensions:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        typing-extensions: ["4.12.1", "4.13.1", "latest"]
    steps:
      - name: Check out repository
        uses: actions/checkout@v5

      - name: Set up python
        uses: actions/setup-python@v5
        with:
          python-version: 3.13

      - name: Install uv
        uses: astral-sh/setup-uv@v6
        with:
          version: "0.6.12"
          enable-cache: true

      - name: Install dependencies
        run: uv sync

      - name: Install typing-extensions
        if: ${{ matrix.typing-extensions != 'latest' }}
        run: uv pip install typing-extensions=="${{ matrix.typing-extensions }}"

      - name: Install typing-extensions
        if: ${{ matrix.typing-extensions == 'latest' }}
        run: uv pip install typing-extensions --upgrade --prerelease=allow

      - name: Test
        run: uv run --no-sync -- python -m pytest tests/unit/test_typing.py


  test_integration:
    name: Test server integration
    runs-on: ubuntu-latest
    strategy:
      matrix:
        uvicorn-version: ["uvicorn<0.27.0", "uvicorn>=0.27.0"]
    steps:
      - name: Check out repository
        uses: actions/checkout@v5

      - name: Set up python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: 3.11

      - name: Install uv
        uses: astral-sh/setup-uv@v6
        with:
          enable-cache: true

      - name: Install Build Dependencies
        run: sudo apt-get install build-essential libpq-dev python3-dev -y

      - name: Install dependencies
        run: |
          uv sync
          uv pip install -U "${{ matrix.uvicorn-version }}"

      - name: Set PYTHONPATH
        run: echo "PYTHONPATH=$PWD" >> $GITHUB_ENV

      - name: Test
        run: uv run --no-sync pytest tests -m server_integration

  test-platform-compat:
    if: github.event_name == 'push' || contains(github.event.pull_request.labels.*.name, 'test platform compat')
    strategy:
      fail-fast: false
      matrix:
        os: ["macos-latest", "windows-latest"]
    uses: ./.github/workflows/test.yml
    with:
      python-version: "3.13"
      os: ${{ matrix.os }}
      timeout: 30

  codeql:
    needs:
      - test
      - validate
    runs-on: ubuntu-latest
    permissions:
      security-events: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v5

      - name: Initialize CodeQL Without Dependencies
        uses: github/codeql-action/init@v3
        with:
          setup-python-dependencies: false

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3

  build-docs:
    needs:
      - validate
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    steps:
      - name: Check out repository
        uses: actions/checkout@v5

      - name: Install Build Dependencies
        run: sudo apt-get install build-essential libpq-dev python3-dev -y

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"
          allow-prereleases: true

      - name: Install uv
        uses: astral-sh/setup-uv@v6
        with:
          enable-cache: true

      - name: Build docs
        run: uv run make docs

      - name: Check docs links
        env:
          LITESTAR_DOCS_IGNORE_MISSING_EXAMPLE_OUTPUT: 1
        run: uv run make docs-linkcheck

      - name: Save PR number
        run: |
          echo "${{ github.event.number }}" > .pr_number

      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: docs-preview
          path: |
            docs/_build/html
            .pr_number
          include-hidden-files: true

  test_minimal_app:
    name: Test Minimal Application with Base Dependencies
    runs-on: ubuntu-latest
    env:
      python_version: "3.12"
    steps:
      - name: Check out repository
        uses: actions/checkout@v5

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install uv
        uses: astral-sh/setup-uv@v6
        with:
          enable-cache: true

      - name: Set pythonpath
        run: echo "PYTHONPATH=$PWD" >> $GITHUB_ENV

      - name: Test
        run: |
          mv tests/examples/test_hello_world.py test_hello_world.py
          uv run pytest test_hello_world.py

  test_pydantic_1_app:
    name: Test Minimal Pydantic 1 application
    runs-on: ubuntu-latest
    env:
      python_version: "3.12"
    steps:
      - name: Check out repository
        uses: actions/checkout@v5

      - name: Install Build Dependencies
        run: sudo apt-get install build-essential libpq-dev python3-dev -y

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install uv
        uses: astral-sh/setup-uv@v6
        with:
          enable-cache: true

      - name: Install dependencies
        run: |
          uv sync
          uv pip install "pydantic==1.*"

      - name: Set pythonpath
        run: echo "PYTHONPATH=$PWD" >> $GITHUB_ENV

      - name: Test
        run: uv run --no-sync coverage run --branch -m unittest test_apps/pydantic_1_app.py

      - name: Rename coverage file
        run: mv .coverage* .coverage.pydantic_v1

      - uses: actions/upload-artifact@v4
        with:
          name: coverage-data-pydantic_v1-${{ inputs.python-version }}
          path: .coverage.pydantic_v1
          include-hidden-files: true

  upload-test-coverage:
    runs-on: ubuntu-latest
    needs:
      - test
      - test_pydantic_1_app
    steps:
      - uses: actions/checkout@v5
      - uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Download Artifacts
        uses: actions/download-artifact@v5
        with:
          pattern: coverage-data*
          merge-multiple: true

      - name: Combine coverage files
        run: |
          python -Im pip install coverage covdefaults
          python -Im coverage combine
          python -Im coverage xml -i

      - name: Fix coverage file name
        run: sed -i "s/home\/runner\/work\/litestar\/litestar/github\/workspace/g" coverage.xml

      - name: Upload coverage reports to Codecov
        uses: codecov/codecov-action@v5
        with:
          files: coverage.xml
          token: ${{ secrets.CODECOV_TOKEN }}
